---
path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/Darwin.swiftmodule/x86_64-apple-macos.swiftmodule'
dependencies:
  - mtime:           1742265563000000000
    path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/Darwin.swiftmodule/x86_64-apple-macos.swiftmodule'
    size:            72136
  - mtime:           1741406594000000000
    path:            'usr/lib/swift/Swift.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1912645
    sdk_relative:    true
  - mtime:           1741407315000000000
    path:            'usr/include/_time.apinotes'
    size:            1132
    sdk_relative:    true
  - mtime:           1741407539000000000
    path:            'usr/lib/swift/_errno.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            3888
    sdk_relative:    true
  - mtime:           1741407553000000000
    path:            'usr/lib/swift/_time.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1063
    sdk_relative:    true
  - mtime:           1741407560000000000
    path:            'usr/lib/swift/_signal.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1099
    sdk_relative:    true
  - mtime:           1741407563000000000
    path:            'usr/lib/swift/sys_time.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1100
    sdk_relative:    true
  - mtime:           1741407555000000000
    path:            'usr/lib/swift/_stdio.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1511
    sdk_relative:    true
  - mtime:           1741407568000000000
    path:            'usr/lib/swift/unistd.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            851
    sdk_relative:    true
  - mtime:           1741407539000000000
    path:            'usr/lib/swift/_math.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            22626
    sdk_relative:    true
  - mtime:           1741406648000000000
    path:            'usr/lib/swift/_Builtin_float.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            5775
    sdk_relative:    true
  - mtime:           1741407582000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            18253
    sdk_relative:    true
version:         1
...
