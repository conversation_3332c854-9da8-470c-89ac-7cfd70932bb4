---
path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/CoreMedia.swiftmodule/x86_64-apple-macos.swiftmodule'
dependencies:
  - mtime:           1742265742000000000
    path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/CoreMedia.swiftmodule/x86_64-apple-macos.swiftmodule'
    size:            389524
  - mtime:           1741406594000000000
    path:            'usr/lib/swift/Swift.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1912645
    sdk_relative:    true
  - mtime:           1741407315000000000
    path:            'usr/include/_time.apinotes'
    size:            1132
    sdk_relative:    true
  - mtime:           1741411513000000000
    path:            'usr/include/ObjectiveC.apinotes'
    size:            11147
    sdk_relative:    true
  - mtime:           1741403049000000000
    path:            'usr/include/Dispatch.apinotes'
    size:            19
    sdk_relative:    true
  - mtime:           1741412759000000000
    path:            'System/Library/Frameworks/CoreAudioTypes.framework/Headers/CoreAudioTypes.apinotes'
    size:            1519
    sdk_relative:    true
  - mtime:           1742085903000000000
    path:            'usr/include/XPC.apinotes'
    size:            123
    sdk_relative:    true
  - mtime:           1741410019000000000
    path:            'System/Library/Frameworks/Security.framework/Headers/Security.apinotes'
    size:            162
    sdk_relative:    true
  - mtime:           1741748427000000000
    path:            'System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes'
    size:            81098
    sdk_relative:    true
  - mtime:           1741407539000000000
    path:            'usr/lib/swift/_errno.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            3888
    sdk_relative:    true
  - mtime:           1741407553000000000
    path:            'usr/lib/swift/_time.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1063
    sdk_relative:    true
  - mtime:           1741407560000000000
    path:            'usr/lib/swift/_signal.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1099
    sdk_relative:    true
  - mtime:           1741407563000000000
    path:            'usr/lib/swift/sys_time.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1100
    sdk_relative:    true
  - mtime:           1741407555000000000
    path:            'usr/lib/swift/_stdio.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1511
    sdk_relative:    true
  - mtime:           1741407568000000000
    path:            'usr/lib/swift/unistd.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            851
    sdk_relative:    true
  - mtime:           1741407539000000000
    path:            'usr/lib/swift/_math.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            22626
    sdk_relative:    true
  - mtime:           1741406648000000000
    path:            'usr/lib/swift/_Builtin_float.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            5775
    sdk_relative:    true
  - mtime:           1741407582000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            18253
    sdk_relative:    true
  - mtime:           1741407856000000000
    path:            'usr/lib/swift/_Concurrency.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            230631
    sdk_relative:    true
  - mtime:           1741408141000000000
    path:            'usr/lib/swift/_StringProcessing.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            22908
    sdk_relative:    true
  - mtime:           1741408713000000000
    path:            'System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            167832
    sdk_relative:    true
  - mtime:           1741408665000000000
    path:            'usr/lib/swift/ObjectiveC.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            6608
    sdk_relative:    true
  - mtime:           1741408850000000000
    path:            'usr/lib/swift/Dispatch.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            57168
    sdk_relative:    true
  - mtime:           1741409051000000000
    path:            'usr/lib/swift/CoreFoundation.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            22951
    sdk_relative:    true
  - mtime:           1741409002000000000
    path:            'usr/lib/swift/XPC.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            33307
    sdk_relative:    true
  - mtime:           1741409206000000000
    path:            'usr/lib/swift/IOKit.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            3649
    sdk_relative:    true
  - mtime:           1741408045000000000
    path:            'usr/lib/swift/Observation.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            3489
    sdk_relative:    true
  - mtime:           1741408729000000000
    path:            'usr/lib/swift/System.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            95502
    sdk_relative:    true
  - mtime:           1741752981000000000
    path:            'System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            995537
    sdk_relative:    true
  - mtime:           1741413361000000000
    path:            'usr/lib/swift/CoreAudio.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            27389
    sdk_relative:    true
  - mtime:           1741753036000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Headers/CoreGraphics.apinotes'
    size:            52901
    sdk_relative:    true
  - mtime:           1741753119000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Modules/CoreGraphics.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            53546
    sdk_relative:    true
  - mtime:           1741412492000000000
    path:            'System/Library/Frameworks/CoreText.framework/Headers/CoreText.apinotes'
    size:            1662
    sdk_relative:    true
  - mtime:           1742180978000000000
    path:            'System/Library/Frameworks/ApplicationServices.framework/Headers/ApplicationServices.apinotes'
    size:            2012
    sdk_relative:    true
  - mtime:           1738796627000000000
    path:            'System/Library/Frameworks/Metal.framework/Headers/Metal.apinotes'
    size:            80245
    sdk_relative:    true
  - mtime:           1741410621000000000
    path:            'System/Library/Frameworks/CoreMedia.framework/Headers/CoreMedia.apinotes'
    size:            65704
    sdk_relative:    true
  - mtime:           1741410325000000000
    path:            'usr/lib/swift/Metal.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            25175
    sdk_relative:    true
  - mtime:           1741412488000000000
    path:            'System/Library/Frameworks/UniformTypeIdentifiers.framework/Headers/UniformTypeIdentifiers.apinotes'
    size:            1666
    sdk_relative:    true
  - mtime:           1741411284000000000
    path:            'usr/lib/swift/UniformTypeIdentifiers.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            20608
    sdk_relative:    true
  - mtime:           1741412555000000000
    path:            'System/Library/Frameworks/CoreText.framework/Modules/CoreText.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1580
    sdk_relative:    true
  - mtime:           1741837641000000000
    path:            'usr/lib/swift/CoreMedia.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            135444
    sdk_relative:    true
version:         1
...
