 swift-stdlib-tool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift-5.0/macosx/libswiftAppKit.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift-5.0/macosx/libswiftCore.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift-5.0/macosx/libswiftCoreAudio.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift-5.0/macosx/libswiftCoreData.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift-5.0/macosx/libswiftCoreFoundation.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift-5.0/macosx/libswiftCoreGraphics.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift-5.0/macosx/libswiftCoreImage.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift-5.0/macosx/libswiftCoreMedia.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift-5.0/macosx/libswiftDarwin.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift-5.0/macosx/libswiftDispatch.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift-5.0/macosx/libswiftFoundation.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift-5.0/macosx/libswiftIOKit.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift-5.0/macosx/libswiftMetal.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift-5.0/macosx/libswiftObjectiveC.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift-5.0/macosx/libswiftQuartzCore.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift-5.0/macosx/libswiftXPC.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift-5.0/macosx/libswiftos.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift-5.0/macosx/libswiftsimd.dylib /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus.debug.dylib @/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Frameworks/libswiftAppKit.dylib @/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Frameworks/libswiftCore.dylib @/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Frameworks/libswiftCoreAudio.dylib @/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Frameworks/libswiftCoreData.dylib @/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Frameworks/libswiftCoreFoundation.dylib @/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Frameworks/libswiftCoreGraphics.dylib @/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Frameworks/libswiftCoreImage.dylib @/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Frameworks/libswiftCoreMedia.dylib @/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Frameworks/libswiftDarwin.dylib @/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Frameworks/libswiftDispatch.dylib @/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Frameworks/libswiftFoundation.dylib @/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Frameworks/libswiftIOKit.dylib @/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Frameworks/libswiftMetal.dylib @/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Frameworks/libswiftObjectiveC.dylib @/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Frameworks/libswiftQuartzCore.dylib @/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Frameworks/libswiftXPC.dylib @/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Frameworks/libswiftos.dylib @/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Frameworks/libswiftsimd.dylib 