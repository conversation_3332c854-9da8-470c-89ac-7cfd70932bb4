{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex": {"is-mutated": true}, "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/EagerLinkingTBDs/Debug": {"is-mutated": true}, "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products": {"is-mutated": true}, "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug": {"is-mutated": true}, "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app": {"is-mutated": true}, "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/__preview.dylib": {"is-mutated": true}, "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus": {"is-mutated": true}, "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus.debug.dylib": {"is-mutated": true}, "<TRIGGER: CodeSign /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/__preview.dylib normal>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus normal>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus.debug.dylib normal>": {"is-command-timestamp": true}, "<TRIGGER: MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>": {"is-command-timestamp": true}, "<TRIGGER: Validate /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>": {"is-command-timestamp": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/EagerLinkingTBDs/Debug", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/__preview.dylib", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/_CodeSignature", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/SDKStatCaches.noindex/macosx15.4-24E241-88b860576fb364319593bd8fb30666b0.sdkstatcache", "<Linked Binary Debug Dylib /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus.debug.dylib>", "<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--begin-linking>", "<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--begin-scanning>", "<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--linker-inputs-ready>", "<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--will-sign>", "<target-Runner-****************************************************************--begin-scanning>", "<target-Runner-****************************************************************--end>", "<target-Runner-****************************************************************--linker-inputs-ready>", "<target-Runner-****************************************************************--modules-ready>", "<workspace-Debug-macosx15.4-macos--stale-file-removal>"], "outputs": ["<all>"]}, "<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a-Debug-macosx--x86_64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/InputFileList-33CC111E2044C6BF0003C045-FlutterInputs-d057753841b7976b70fc76b956a78b2a-resolved.xcfilelist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/OutputFileList-33CC111E2044C6BF0003C045-FlutterOutputs-36263e2a36d06ad58f39863a7bf8a614-resolved.xcfilelist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/Script-33CC111E2044C6BF0003C045.sh"], "roots": ["/tmp/Runner.dst", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products"], "outputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a-Debug-macosx--x86_64-build-headers-stale-file-removal>"]}, "<target-Runner-****************************************************************-Debug-macosx--x86_64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/_CodeSignature", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/__preview.dylib", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus.debug.dylib", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/thinned", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/unthinned", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources/Base.lproj/MainMenu.nib", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources/Base.lproj/MainMenu~iphone.nib", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources/Base.lproj/MainMenu~ipad.nib", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Base.lproj/MainMenu-PartialInfo.plist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources/Assets.car", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_signature", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/thinned", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/unthinned", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Info.plist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/PkgInfo", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.app.xcent", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.app.xcent.der", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner Swift Compilation Finished", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/MainFlutterWindow.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/MainFlutterWindow.swiftconstvalues", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/AppDelegate.swiftconstvalues", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.swiftconstvalues", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-ExecutorLinkFileList-normal-x86_64.txt", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/x86_64-apple-macos.abi.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/x86_64-apple-macos.swiftdoc", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/x86_64-apple-macos.swiftmodule", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/__preview.dylib", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus.debug.dylib", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus_lto.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus_dependency_info.dat", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner Swift Compilation Requirements Finished", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftmodule", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftsourceinfo", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.abi.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus-Swift.h", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftdoc", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/learnfocus-Swift.h", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/Entitlements.plist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner_const_extract_protocols.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.LinkFileList", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.SwiftConstValuesFileList", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.SwiftFileList", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Script-3399D490228B24CF009A79C7.sh", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-DebugDylibInstallName-normal-x86_64.txt", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-DebugDylibPath-normal-x86_64.txt", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-all-non-framework-target-headers.hmap", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-all-target-headers.hmap", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-generated-files.hmap", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-own-target-headers.hmap", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-project-headers.hmap", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.DependencyMetadataFileList", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.hmap"], "roots": ["/tmp/Runner.dst", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products"], "outputs": ["<target-Runner-****************************************************************-Debug-macosx--x86_64-build-headers-stale-file-removal>"]}, "<workspace-Debug-macosx15.4-macos--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner-18c1723432283e0cc55f10a6dcfd9e02-VFS/all-product-headers.yaml"], "outputs": ["<workspace-Debug-macosx15.4-macos--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/SDKStatCaches.noindex/macosx15.4-24E241-88b860576fb364319593bd8fb30666b0.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/SDKStatCaches.noindex/macosx15.4-24E241-88b860576fb364319593bd8fb30666b0.sdkstatcache", "inputs": [], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/SDKStatCaches.noindex/macosx15.4-24E241-88b860576fb364319593bd8fb30666b0.sdkstatcache", "<ClangStatCache /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/SDKStatCaches.noindex/macosx15.4-24E241-88b860576fb364319593bd8fb30666b0.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/SDKStatCaches.noindex/macosx15.4-24E241-88b860576fb364319593bd8fb30666b0.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos", "signature": "48b60ea685f169e58b75ce39deac1800"}, "P0:::CreateBuildDirectory /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex"]}, "P0:::CreateBuildDirectory /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/EagerLinkingTBDs/Debug": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/EagerLinkingTBDs/Debug", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/EagerLinkingTBDs/Debug>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/EagerLinkingTBDs/Debug"]}, "P0:::CreateBuildDirectory /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products"]}, "P0:::CreateBuildDirectory /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner-18c1723432283e0cc55f10a6dcfd9e02-VFS/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--fused-phase0-run-script": {"tool": "phony", "inputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--start>", "<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--begin-compiling>", "<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e02f4f5bcf30ee9448700f1908916ccb6e2-target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a->", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/InputFileList-33CC111E2044C6BF0003C045-FlutterInputs-d057753841b7976b70fc76b956a78b2a-resolved.xcfilelist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/OutputFileList-33CC111E2044C6BF0003C045-FlutterOutputs-36263e2a36d06ad58f39863a7bf8a614-resolved.xcfilelist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/Script-33CC111E2044C6BF0003C045.sh"], "outputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--fused-phase0-run-script>"]}, "P0:::Gate target-Runner-****************************************************************--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--fused-phase2-run-script>", "<target-Runner-****************************************************************--begin-compiling>", "<ExtractAppIntentsMetadata /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources/Metadata.appintents>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.SwiftConstValuesFileList", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.DependencyMetadataFileList", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.DependencyStaticMetadataFileList"], "outputs": ["<target-Runner-****************************************************************--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--Barrier-ChangePermissions>", "<target-Runner-****************************************************************--will-sign>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-Runner-****************************************************************--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--Barrier-StripSymbols>", "<target-Runner-****************************************************************--will-sign>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--Barrier-ChangePermissions>"]}, "P0:::Gate target-Runner-****************************************************************--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-Runner-****************************************************************--will-sign>", "<target-Runner-****************************************************************--begin-compiling>", "<CodeSign /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>", "<CodeSign /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/__preview.dylib>", "<CodeSign /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus.debug.dylib>"], "outputs": ["<target-Runner-****************************************************************--Barrier-CodeSign>"]}, "P0:::Gate target-Runner-****************************************************************--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--Barrier-GenerateStubAPI>", "<target-Runner-****************************************************************--will-sign>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--Barrier-CopyAside>"]}, "P0:::Gate target-Runner-****************************************************************--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ProductPostprocessingTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-Runner-****************************************************************--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--Barrier-CodeSign>", "<target-Runner-****************************************************************--will-sign>", "<target-Runner-****************************************************************--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>"], "outputs": ["<target-Runner-****************************************************************--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-Runner-****************************************************************--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--Barrier-Validate>", "<target-Runner-****************************************************************--will-sign>", "<target-Runner-****************************************************************--begin-compiling>", "<LSRegisterURL /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>", "<Touch /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>"], "outputs": ["<target-Runner-****************************************************************--Barrier-RegisterProduct>"]}, "P0:::Gate target-Runner-****************************************************************--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--Barrier-CopyAside>", "<target-Runner-****************************************************************--will-sign>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--Barrier-StripSymbols>"]}, "P0:::Gate target-Runner-****************************************************************--Barrier-Validate": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-Runner-****************************************************************--will-sign>", "<target-Runner-****************************************************************--begin-compiling>", "<Validate /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>"], "outputs": ["<target-Runner-****************************************************************--Barrier-Validate>"]}, "P0:::Gate target-Runner-****************************************************************--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--CustomTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--CustomTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--DocumentationTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--GeneratedFilesTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ProductStructureTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.app.xcent", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.app.xcent.der", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/Entitlements.plist"], "outputs": ["<target-Runner-****************************************************************--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-all-non-framework-target-headers.hmap", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-all-target-headers.hmap", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-generated-files.hmap", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-own-target-headers.hmap", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-project-headers.hmap", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.hmap"], "outputs": ["<target-Runner-****************************************************************--HeadermapTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Info.plist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/PkgInfo"], "outputs": ["<target-Runner-****************************************************************--InfoPlistTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--ModuleMapTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--RealityAssetsTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--fused-phase2-run-script>", "<target-Runner-****************************************************************--ModuleMapTaskProducer>", "<target-Runner-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-Runner-****************************************************************--InfoPlistTaskProducer>", "<target-Runner-****************************************************************--SanitizerTaskProducer>", "<target-Runner-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-Runner-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-Runner-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-Runner-****************************************************************--TestTargetTaskProducer>", "<target-Runner-****************************************************************--TestHostTaskProducer>", "<target-Runner-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-Runner-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-Runner-****************************************************************--DocumentationTaskProducer>", "<target-Runner-****************************************************************--CustomTaskProducer>", "<target-Runner-****************************************************************--StubBinaryTaskProducer>", "<target-Runner-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--start>", "<target-Runner-****************************************************************--begin-compiling>", "<MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>", "<MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents>", "<MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS>", "<MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources>"], "outputs": ["<target-Runner-****************************************************************--ProductStructureTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--HeadermapTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--RealityAssetsTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--SanitizerTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--StubBinaryTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--fused-phase2-run-script>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--fused-phase2-run-script>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--fused-phase2-run-script>", "<target-Runner-****************************************************************--begin-compiling>", "<CopySwiftStdlib /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>"], "outputs": ["<target-Runner-****************************************************************--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--TestHostTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ProductPostprocessingTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--TestTargetTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--copy-headers-completion": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-Runner-****************************************************************--copy-headers-completion>"]}, "P0:::Gate target-Runner-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources/Base.lproj/MainMenu.nib", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources/Base.lproj/MainMenu~iphone.nib", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources/Base.lproj/MainMenu~ipad.nib", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Base.lproj/MainMenu-PartialInfo.plist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources/Assets.car", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_signature", "<MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/unthinned>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner Swift Compilation Finished", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/MainFlutterWindow.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/MainFlutterWindow.swiftconstvalues", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/AppDelegate.swiftconstvalues", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.swiftconstvalues", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-ExecutorLinkFileList-normal-x86_64.txt", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/x86_64-apple-macos.abi.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/x86_64-apple-macos.swiftdoc", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/x86_64-apple-macos.swiftmodule", "<Linked Binary Preview Injection Dylib /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/__preview.dylib>", "<Linked Binary /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus_lto.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus_dependency_info.dat", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner Swift Compilation Requirements Finished", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftmodule", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftsourceinfo", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.abi.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus-Swift.h", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftdoc", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner_const_extract_protocols.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.LinkFileList", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.SwiftFileList", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-DebugDylibInstallName-normal-x86_64.txt", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-DebugDylibPath-normal-x86_64.txt"], "outputs": ["<target-Runner-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-Runner-****************************************************************--fused-phase1-copy-files": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--fused-phase1-copy-files>"]}, "P0:::Gate target-Runner-****************************************************************--fused-phase2-run-script": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--fused-phase1-copy-files>", "<target-Runner-****************************************************************--begin-compiling>", "<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e0280fce6e78b27fa69321adec3779466d5-target-Runner-****************************************************************->", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Script-3399D490228B24CF009A79C7.sh"], "outputs": ["<target-Runner-****************************************************************--fused-phase2-run-script>"]}, "P0:::Gate target-Runner-****************************************************************--generated-headers": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-Runner-****************************************************************--generated-headers>"]}, "P0:::Gate target-Runner-****************************************************************--swift-generated-headers": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner Swift Compilation Requirements Finished", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftmodule", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftsourceinfo", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.abi.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus-Swift.h", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftdoc", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/learnfocus-Swift.h"], "outputs": ["<target-Runner-****************************************************************--swift-generated-headers>"]}, "P0:target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a-:Debug:Gate target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--begin-compiling": {"tool": "phony", "inputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a-Debug-macosx--x86_64-build-headers-stale-file-removal>"], "outputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--begin-compiling>"]}, "P0:target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a-:Debug:Gate target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--begin-linking": {"tool": "phony", "inputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a-Debug-macosx--x86_64-build-headers-stale-file-removal>"], "outputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--begin-linking>"]}, "P0:target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a-:Debug:Gate target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--begin-scanning": {"tool": "phony", "inputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a-Debug-macosx--x86_64-build-headers-stale-file-removal>", "<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--begin-compiling>"], "outputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--begin-scanning>"]}, "P0:target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a-:Debug:Gate target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--end": {"tool": "phony", "inputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--entry>", "<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e02f4f5bcf30ee9448700f1908916ccb6e2-target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a->", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/InputFileList-33CC111E2044C6BF0003C045-FlutterInputs-d057753841b7976b70fc76b956a78b2a-resolved.xcfilelist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/OutputFileList-33CC111E2044C6BF0003C045-FlutterOutputs-36263e2a36d06ad58f39863a7bf8a614-resolved.xcfilelist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/Script-33CC111E2044C6BF0003C045.sh", "<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--fused-phase0-run-script>"], "outputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--end>"]}, "P0:target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a-:Debug:Gate target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--entry": {"tool": "phony", "inputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a-Debug-macosx--x86_64-build-headers-stale-file-removal>", "<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--begin-compiling>"], "outputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--entry>"]}, "P0:target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a-:Debug:Gate target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--immediate": {"tool": "phony", "inputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a-Debug-macosx--x86_64-build-headers-stale-file-removal>"], "outputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--immediate>"]}, "P0:target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a-:Debug:Gate target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--begin-compiling>"], "outputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--linker-inputs-ready>"]}, "P0:target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a-:Debug:Gate target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--modules-ready": {"tool": "phony", "inputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--begin-compiling>", "<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e02f4f5bcf30ee9448700f1908916ccb6e2-target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a->", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/InputFileList-33CC111E2044C6BF0003C045-FlutterInputs-d057753841b7976b70fc76b956a78b2a-resolved.xcfilelist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/OutputFileList-33CC111E2044C6BF0003C045-FlutterOutputs-36263e2a36d06ad58f39863a7bf8a614-resolved.xcfilelist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/Script-33CC111E2044C6BF0003C045.sh"], "outputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--modules-ready>"]}, "P0:target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a-:Debug:Gate target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--begin-compiling>", "<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e02f4f5bcf30ee9448700f1908916ccb6e2-target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a->", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/InputFileList-33CC111E2044C6BF0003C045-FlutterInputs-d057753841b7976b70fc76b956a78b2a-resolved.xcfilelist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/OutputFileList-33CC111E2044C6BF0003C045-FlutterOutputs-36263e2a36d06ad58f39863a7bf8a614-resolved.xcfilelist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/Script-33CC111E2044C6BF0003C045.sh"], "outputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--unsigned-product-ready>"]}, "P0:target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a-:Debug:Gate target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--will-sign": {"tool": "phony", "inputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--unsigned-product-ready>"], "outputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--will-sign>"]}, "P0:target-Runner-****************************************************************-:Debug:CodeSign /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.app.xcent/", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Info.plist/", "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Flutter/GeneratedPluginRegistrant.swift/", "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/AppDelegate.swift/", "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Assets.xcassets/", "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Base.lproj/MainMenu.xib/", "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/MainFlutterWindow.swift/", "<CodeSign /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus.debug.dylib>", "<CodeSign /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/__preview.dylib>", "<target-Runner-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-Runner-****************************************************************--will-sign>", "<target-Runner-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus normal>", "<TRIGGER: MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/_CodeSignature", "<CodeSign /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>", "<TRIGGER: CodeSign /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>"]}, "P0:target-Runner-****************************************************************-:Debug:CodeSign /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/__preview.dylib": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/__preview.dylib", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Info.plist/", "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Flutter/GeneratedPluginRegistrant.swift/", "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/AppDelegate.swift/", "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Assets.xcassets/", "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Base.lproj/MainMenu.xib/", "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/MainFlutterWindow.swift/", "<CodeSign /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus.debug.dylib>", "<target-Runner-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-Runner-****************************************************************--will-sign>", "<target-Runner-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/__preview.dylib normal>"], "outputs": ["<CodeSign /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/__preview.dylib>"]}, "P0:target-Runner-****************************************************************-:Debug:CodeSign /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus.debug.dylib": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus.debug.dylib", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Info.plist/", "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Flutter/GeneratedPluginRegistrant.swift/", "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/AppDelegate.swift/", "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Assets.xcassets/", "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Base.lproj/MainMenu.xib/", "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/MainFlutterWindow.swift/", "<target-Runner-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-Runner-****************************************************************--will-sign>", "<target-Runner-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus.debug.dylib normal>"], "outputs": ["<CodeSign /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus.debug.dylib>"]}, "P0:target-Runner-****************************************************************-:Debug:CompileAssetCatalogVariant thinned /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources /Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant thinned /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources /Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Assets.xcassets", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Assets.xcassets/", "<MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/thinned>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/thinned", "<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_generated_info.plist_thinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Assets.xcassets", "--compile", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/thinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_dependencies_thinned", "--output-partial-info-plist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_generated_info.plist_thinned", "--app-icon", "AppIcon", "--enable-on-demand-resources", "NO", "--development-region", "en", "--target-device", "mac", "--minimum-deployment-target", "10.14", "--platform", "macosx"], "env": {}, "working-directory": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos", "control-enabled": false, "deps": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_dependencies_thinned"], "deps-style": "dependency-info", "signature": "1829731987f917bddf04d35e4835c441"}, "P0:target-Runner-****************************************************************-:Debug:CompileAssetCatalogVariant unthinned /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources /Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant unthinned /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources /Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Assets.xcassets", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Assets.xcassets/", "<MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/unthinned>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/unthinned", "<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_generated_info.plist_unthinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Assets.xcassets", "--compile", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/unthinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_dependencies_unthinned", "--output-partial-info-plist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_generated_info.plist_unthinned", "--app-icon", "AppIcon", "--enable-on-demand-resources", "NO", "--development-region", "en", "--target-device", "mac", "--minimum-deployment-target", "10.14", "--platform", "macosx"], "env": {}, "working-directory": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos", "control-enabled": false, "deps": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_dependencies_unthinned"], "deps-style": "dependency-info", "signature": "40c96d01ba0e10b89c35ab0e22de7524"}, "P0:target-Runner-****************************************************************-:Debug:CompileXIB /Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Base.lproj/MainMenu.xib": {"tool": "shell", "description": "CompileXIB /Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Base.lproj/MainMenu.xib", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Base.lproj/MainMenu.xib", "<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources/Base.lproj/MainMenu.nib", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources/Base.lproj/MainMenu~iphone.nib", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources/Base.lproj/MainMenu~ipad.nib", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Base.lproj/MainMenu-PartialInfo.plist"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool", "--errors", "--warnings", "--notices", "--module", "learnfocus", "--output-partial-info-plist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Base.lproj/MainMenu-PartialInfo.plist", "--auto-activate-custom-fonts", "--target-device", "mac", "--minimum-deployment-target", "10.14", "--output-format", "human-readable-text", "--compile", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources/Base.lproj/MainMenu.nib", "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Base.lproj/MainMenu.xib"], "env": {"XCODE_DEVELOPER_USR_PATH": "/Applications/Xcode.app/Contents/Developer/usr/bin/.."}, "working-directory": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos", "control-enabled": false, "signature": "2e34ab8d47ea85d0ac229b5fbc98fe8e"}, "P0:target-Runner-****************************************************************-:Debug:CopySwiftLibs /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app": {"tool": "embed-swift-stdlib", "description": "CopySwiftLibs /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus.debug.dylib", "<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--fused-phase2-run-script>", "<target-Runner-****************************************************************--immediate>"], "outputs": ["<CopySwiftStdlib /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>"], "deps": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/SwiftStdLibToolInputDependencies.dep"}, "P0:target-Runner-****************************************************************-:Debug:ExtractAppIntentsMetadata": {"tool": "shell", "description": "ExtractAppIntentsMetadata", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/MainFlutterWindow.swift", "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/AppDelegate.swift", "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Flutter/GeneratedPluginRegistrant.swift", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/MainFlutterWindow.swiftconstvalues", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/AppDelegate.swiftconstvalues", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.swiftconstvalues", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.DependencyMetadataFileList", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus_dependency_info.dat", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.SwiftFileList", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.SwiftConstValuesFileList", "<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--fused-phase2-run-script>", "<target-Runner-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources/Metadata.appintents>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsmetadataprocessor", "--toolchain-dir", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "--module-name", "learnfocus", "--sdk-root", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "--xcode-version", "16E140", "--platform-family", "macOS", "--deployment-target", "10.14", "--bundle-identifier", "com.example.learnfocus", "--output", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources", "--target-triple", "x86_64-apple-macos10.14", "--binary-file", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus", "--dependency-file", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus_dependency_info.dat", "--stringsdata-file", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "--source-file-list", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.SwiftFileList", "--metadata-file-list", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.DependencyMetadataFileList", "--static-metadata-file-list", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.DependencyStaticMetadataFileList", "--swift-const-vals-list", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.SwiftConstValuesFileList", "--compile-time-extraction", "--deployment-aware-processing", "--validate-assistant-intents", "--no-app-shortcuts-localization"], "env": {}, "working-directory": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos", "signature": "89213609c44f5b8b207e282a5b700fc3"}, "P0:target-Runner-****************************************************************-:Debug:Gate target-Runner-****************************************************************--begin-compiling": {"tool": "phony", "inputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--modules-ready>", "<target-Runner-****************************************************************-Debug-macosx--x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Runner.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug>", "<CreateBuildDirectory-/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/EagerLinkingTBDs/Debug>"], "outputs": ["<target-Runner-****************************************************************--begin-compiling>"]}, "P0:target-Runner-****************************************************************-:Debug:Gate target-Runner-****************************************************************--begin-linking": {"tool": "phony", "inputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--end>", "<target-Runner-****************************************************************-Debug-macosx--x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Runner.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug>", "<CreateBuildDirectory-/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/EagerLinkingTBDs/Debug>"], "outputs": ["<target-Runner-****************************************************************--begin-linking>"]}, "P0:target-Runner-****************************************************************-:Debug:Gate target-Runner-****************************************************************--begin-scanning": {"tool": "phony", "inputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--scan-inputs-ready>", "<target-Runner-****************************************************************-Debug-macosx--x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Runner.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug>", "<CreateBuildDirectory-/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/EagerLinkingTBDs/Debug>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--begin-scanning>"]}, "P0:target-Runner-****************************************************************-:Debug:Gate target-Runner-****************************************************************--end": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--entry>", "<CodeSign /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>", "<CodeSign /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/__preview.dylib>", "<CodeSign /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus.debug.dylib>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources/Base.lproj/MainMenu.nib", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources/Base.lproj/MainMenu~iphone.nib", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources/Base.lproj/MainMenu~ipad.nib", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Base.lproj/MainMenu-PartialInfo.plist", "<CopySwiftStdlib /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>", "<ExtractAppIntentsMetadata /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources/Metadata.appintents>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources/Assets.car", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_signature", "<MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/unthinned>", "<MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>", "<MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents>", "<MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS>", "<MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Info.plist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/PkgInfo", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.app.xcent", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.app.xcent.der", "<RegisterExecutionPolicyException /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>", "<LSRegisterURL /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner Swift Compilation Finished", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/MainFlutterWindow.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/MainFlutterWindow.swiftconstvalues", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/AppDelegate.swiftconstvalues", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.swiftconstvalues", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "<Touch /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>", "<Validate /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-ExecutorLinkFileList-normal-x86_64.txt", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/x86_64-apple-macos.abi.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/x86_64-apple-macos.swiftdoc", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/x86_64-apple-macos.swiftmodule", "<Linked Binary Preview Injection Dylib /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/__preview.dylib>", "<Linked Binary /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus_lto.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus_dependency_info.dat", "<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e0280fce6e78b27fa69321adec3779466d5-target-Runner-****************************************************************->", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner Swift Compilation Requirements Finished", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftmodule", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftsourceinfo", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.abi.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus-Swift.h", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftdoc", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/learnfocus-Swift.h", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/learnfocus-Swift.h", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/Entitlements.plist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner_const_extract_protocols.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.LinkFileList", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.SwiftConstValuesFileList", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.SwiftFileList", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Script-3399D490228B24CF009A79C7.sh", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-DebugDylibInstallName-normal-x86_64.txt", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-DebugDylibPath-normal-x86_64.txt", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-all-non-framework-target-headers.hmap", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-all-target-headers.hmap", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-generated-files.hmap", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-own-target-headers.hmap", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-project-headers.hmap", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.DependencyMetadataFileList", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.hmap", "<target-Runner-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-Runner-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-Runner-****************************************************************--Barrier-ChangePermissions>", "<target-Runner-****************************************************************--Barrier-CodeSign>", "<target-Runner-****************************************************************--Barrier-CopyAside>", "<target-Runner-****************************************************************--Barrier-GenerateStubAPI>", "<target-Runner-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-Runner-****************************************************************--Barrier-RegisterProduct>", "<target-Runner-****************************************************************--Barrier-StripSymbols>", "<target-Runner-****************************************************************--Barrier-Validate>", "<target-Runner-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-Runner-****************************************************************--CustomTaskProducer>", "<target-Runner-****************************************************************--DocumentationTaskProducer>", "<target-Runner-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-Runner-****************************************************************--GeneratedFilesTaskProducer>", "<target-Runner-****************************************************************--HeadermapTaskProducer>", "<target-Runner-****************************************************************--InfoPlistTaskProducer>", "<target-Runner-****************************************************************--ModuleMapTaskProducer>", "<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--ProductPostprocessingTaskProducer>", "<target-Runner-****************************************************************--ProductStructureTaskProducer>", "<target-Runner-****************************************************************--RealityAssetsTaskProducer>", "<target-Runner-****************************************************************--SanitizerTaskProducer>", "<target-Runner-****************************************************************--StubBinaryTaskProducer>", "<target-Runner-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-Runner-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-Runner-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-Runner-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-Runner-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-Runner-****************************************************************--TestHostTaskProducer>", "<target-Runner-****************************************************************--TestTargetPostprocessingTaskProducer>", "<target-Runner-****************************************************************--TestTargetTaskProducer>", "<target-Runner-****************************************************************--copy-headers-completion>", "<target-Runner-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Runner-****************************************************************--fused-phase1-copy-files>", "<target-Runner-****************************************************************--fused-phase2-run-script>", "<target-Runner-****************************************************************--generated-headers>", "<target-Runner-****************************************************************--swift-generated-headers>"], "outputs": ["<target-Runner-****************************************************************--end>"]}, "P0:target-Runner-****************************************************************-:Debug:Gate target-Runner-****************************************************************--entry": {"tool": "phony", "inputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--end>", "<target-Runner-****************************************************************-Debug-macosx--x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Runner.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug>", "<CreateBuildDirectory-/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/EagerLinkingTBDs/Debug>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--entry>"]}, "P0:target-Runner-****************************************************************-:Debug:Gate target-Runner-****************************************************************--immediate": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************-Debug-macosx--x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Runner.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug>", "<CreateBuildDirectory-/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/EagerLinkingTBDs/Debug>"], "outputs": ["<target-Runner-****************************************************************--immediate>"]}, "P0:target-Runner-****************************************************************-:Debug:Gate target-Runner-****************************************************************--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-ExecutorLinkFileList-normal-x86_64.txt", "<Linked Binary Preview Injection Dylib /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/__preview.dylib>", "<Linked Binary /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus_lto.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus_dependency_info.dat", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner Swift Compilation Requirements Finished", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftmodule", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftsourceinfo", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.abi.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus-Swift.h", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftdoc", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.LinkFileList", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-DebugDylibInstallName-normal-x86_64.txt", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-DebugDylibPath-normal-x86_64.txt"], "outputs": ["<target-Runner-****************************************************************--linker-inputs-ready>"]}, "P0:target-Runner-****************************************************************-:Debug:Gate target-Runner-****************************************************************--modules-ready": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/x86_64-apple-macos.abi.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/x86_64-apple-macos.swiftdoc", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/x86_64-apple-macos.swiftmodule", "<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e0280fce6e78b27fa69321adec3779466d5-target-Runner-****************************************************************->", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner Swift Compilation Requirements Finished", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftmodule", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftsourceinfo", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.abi.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus-Swift.h", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftdoc", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/learnfocus-Swift.h", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Script-3399D490228B24CF009A79C7.sh"], "outputs": ["<target-Runner-****************************************************************--modules-ready>"]}, "P0:target-Runner-****************************************************************-:Debug:Gate target-Runner-****************************************************************--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>", "<ExtractAppIntentsMetadata /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources/Metadata.appintents>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources/Assets.car", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_signature", "<MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/unthinned>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.app.xcent", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.app.xcent.der", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner Swift Compilation Finished", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/MainFlutterWindow.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/MainFlutterWindow.swiftconstvalues", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/AppDelegate.swiftconstvalues", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.swiftconstvalues", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-ExecutorLinkFileList-normal-x86_64.txt", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/x86_64-apple-macos.abi.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/x86_64-apple-macos.swiftdoc", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/x86_64-apple-macos.swiftmodule", "<Linked Binary Preview Injection Dylib /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/__preview.dylib>", "<Linked Binary /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus_lto.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus_dependency_info.dat", "<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e0280fce6e78b27fa69321adec3779466d5-target-Runner-****************************************************************->", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner Swift Compilation Requirements Finished", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftmodule", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftsourceinfo", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.abi.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus-Swift.h", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftdoc", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/learnfocus-Swift.h", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/Entitlements.plist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner_const_extract_protocols.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.LinkFileList", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.SwiftConstValuesFileList", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.SwiftFileList", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Script-3399D490228B24CF009A79C7.sh", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-DebugDylibInstallName-normal-x86_64.txt", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-DebugDylibPath-normal-x86_64.txt", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.DependencyMetadataFileList", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.DependencyStaticMetadataFileList", "<target-Runner-****************************************************************--Barrier-GenerateStubAPI>"], "outputs": ["<target-Runner-****************************************************************--unsigned-product-ready>"]}, "P0:target-Runner-****************************************************************-:Debug:Gate target-Runner-****************************************************************--will-sign": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--unsigned-product-ready>"], "outputs": ["<target-Runner-****************************************************************--will-sign>"]}, "P0:target-Runner-****************************************************************-:Debug:GenerateAssetSymbols /Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Assets.xcassets": {"tool": "shell", "description": "GenerateAssetSymbols /Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Assets.xcassets", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Assets.xcassets/", "<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Assets.xcassets", "--compile", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--enable-on-demand-resources", "NO", "--development-region", "en", "--target-device", "mac", "--minimum-deployment-target", "10.14", "--platform", "macosx", "--bundle-identifier", "com.example.learnfocus", "--generate-swift-asset-symbols", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/GeneratedAssetSymbols.swift", "--generate-objc-asset-symbols", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/GeneratedAssetSymbols.h", "--generate-asset-symbol-index", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "env": {}, "working-directory": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos", "control-enabled": false, "signature": "914b5a1b178873f9271096b6a02441f7"}, "P0:target-Runner-****************************************************************-:Debug:LinkAssetCatalog /Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Assets.xcassets": {"tool": "link-assetcatalog", "description": "LinkAssetCatalog /Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Assets.xcassets", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Assets.xcassets/", "<MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_signature", "<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources/Assets.car"], "deps": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_dependencies"}, "P0:target-Runner-****************************************************************-:Debug:LinkAssetCatalogSignature": {"tool": "link-assetcatalog", "description": "LinkAssetCatalogSignature", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_signature"], "always-out-of-date": true}, "P0:target-Runner-****************************************************************-:Debug:MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/thinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/thinned", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/thinned", "<MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/thinned>"]}, "P0:target-Runner-****************************************************************-:Debug:MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/unthinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/unthinned", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/unthinned", "<MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_output/unthinned>"]}, "P0:target-Runner-****************************************************************-:Debug:MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app", "inputs": ["<target-Runner-****************************************************************--start>", "<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app", "<MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>", "<TRIGGER: MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>"]}, "P0:target-Runner-****************************************************************-:Debug:MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents", "inputs": ["<target-Runner-****************************************************************--start>", "<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents", "<MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents>"]}, "P0:target-Runner-****************************************************************-:Debug:MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS", "inputs": ["<target-Runner-****************************************************************--start>", "<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS", "<MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS>"]}, "P0:target-Runner-****************************************************************-:Debug:MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources", "inputs": ["<target-Runner-****************************************************************--start>", "<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources", "<MkDir /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources>"]}, "P0:target-Runner-****************************************************************-:Debug:ProcessInfoPlistFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Info.plist /Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Info.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Info.plist /Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Info.plist", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Info.plist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Base.lproj/MainMenu-PartialInfo.plist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/assetcatalog_generated_info.plist", "<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Info.plist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/PkgInfo"]}, "P0:target-Runner-****************************************************************-:Debug:ProcessProductPackaging /Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/DebugProfile.entitlements /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.app.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging /Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/DebugProfile.entitlements /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.app.xcent", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/DebugProfile.entitlements", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/Entitlements.plist", "<target-Runner-****************************************************************--ProductStructureTaskProducer>", "<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.app.xcent"]}, "P0:target-Runner-****************************************************************-:Debug:ProcessProductPackagingDER /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.app.xcent /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.app.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.app.xcent /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.app.xcent.der", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.app.xcent", "<target-Runner-****************************************************************--ProductStructureTaskProducer>", "<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.app.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.app.xcent", "-o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.app.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos", "signature": "07c7605295e8f7ceb0e80898220f92a3"}, "P0:target-Runner-****************************************************************-:Debug:RegisterExecutionPolicyException /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app", "<target-Runner-****************************************************************--Barrier-CodeSign>", "<target-Runner-****************************************************************--will-sign>", "<target-Runner-****************************************************************--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>"]}, "P0:target-Runner-****************************************************************-:Debug:RegisterWithLaunchServices /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app": {"tool": "lsregisterurl", "description": "RegisterWithLaunchServices /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app", "inputs": ["<target-Runner-****************************************************************--Barrier-Validate>", "<target-Runner-****************************************************************--will-sign>", "<target-Runner-****************************************************************--entry>", "<TRIGGER: Validate /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>"], "outputs": ["<LSRegisterURL /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>"]}, "P0:target-Runner-****************************************************************-:Debug:SwiftDriver Compilation Runner normal x86_64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation Runner normal x86_64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/MainFlutterWindow.swift", "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/AppDelegate.swift", "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Flutter/GeneratedPluginRegistrant.swift", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.SwiftFileList", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner_const_extract_protocols.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-generated-files.hmap", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-own-target-headers.hmap", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-all-target-headers.hmap", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-project-headers.hmap", "<ClangStatCache /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/SDKStatCaches.noindex/macosx15.4-24E241-88b860576fb364319593bd8fb30666b0.sdkstatcache>", "<target-Runner-****************************************************************--generated-headers>", "<target-Runner-****************************************************************--copy-headers-completion>", "<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner Swift Compilation Finished", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/MainFlutterWindow.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/MainFlutterWindow.swiftconstvalues", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/AppDelegate.swiftconstvalues", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.swiftconstvalues", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues"]}, "P0:target-Runner-****************************************************************-:Debug:Touch /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app": {"tool": "shell", "description": "Touch /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app", "<target-Runner-****************************************************************--Barrier-Validate>", "<target-Runner-****************************************************************--will-sign>", "<target-Runner-****************************************************************--entry>"], "outputs": ["<Touch /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app"], "env": {}, "working-directory": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos", "signature": "fed16efe53b5737193cf207fcdf250c4"}, "P0:target-Runner-****************************************************************-:Debug:Validate /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app": {"tool": "validate-product", "description": "Validate /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Info.plist", "<target-Runner-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-Runner-****************************************************************--will-sign>", "<target-Runner-****************************************************************--entry>", "<TRIGGER: CodeSign /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>"], "outputs": ["<Validate /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>", "<TRIGGER: Validate /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app>"]}, "P2:::WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner-18c1723432283e0cc55f10a6dcfd9e02-VFS/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner-18c1723432283e0cc55f10a6dcfd9e02-VFS/all-product-headers.yaml", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner-18c1723432283e0cc55f10a6dcfd9e02-VFS/all-product-headers.yaml"]}, "P2:target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a-:Debug:PhaseScriptExecution Run Script /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/Script-33CC111E2044C6BF0003C045.sh": {"tool": "shell", "description": "PhaseScriptExecution Run Script /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/Script-33CC111E2044C6BF0003C045.sh", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Flutter/ephemeral/tripwire", "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Flutter/ephemeral/FlutterInputs.xcfilelist", "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Flutter/ephemeral/FlutterOutputs.xcfilelist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/InputFileList-33CC111E2044C6BF0003C045-FlutterInputs-d057753841b7976b70fc76b956a78b2a-resolved.xcfilelist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/OutputFileList-33CC111E2044C6BF0003C045-FlutterOutputs-36263e2a36d06ad58f39863a7bf8a614-resolved.xcfilelist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/Script-33CC111E2044C6BF0003C045.sh", "<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--start>", "<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--entry>"], "outputs": ["<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e02f4f5bcf30ee9448700f1908916ccb6e2-target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a->"], "args": ["/bin/sh", "-c", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter\\ Assemble.build/Script-33CC111E2044C6BF0003C045.sh"], "env": {"ACTION": "build", "AD_HOC_CODE_SIGNING_ALLOWED": "YES", "ALLOW_BUILD_REQUEST_OVERRIDES": "NO", "ALLOW_TARGET_PLATFORM_SPECIALIZATION": "NO", "ALTERNATE_GROUP": "staff", "ALTERNATE_MODE": "u+w,go-w,a+rX", "ALTERNATE_OWNER": "govo", "ALTERNATIVE_DISTRIBUTION_WEB": "NO", "ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES": "NO", "ALWAYS_SEARCH_USER_PATHS": "NO", "ALWAYS_USE_SEPARATE_HEADERMAPS": "NO", "APPLICATION_EXTENSION_API_ONLY": "NO", "APPLY_RULES_IN_COPY_FILES": "NO", "APPLY_RULES_IN_COPY_HEADERS": "NO", "APP_SHORTCUTS_ENABLE_FLEXIBLE_MATCHING": "YES", "ARCHS": "x86_64", "ARCHS_STANDARD": "arm64 x86_64", "ARCHS_STANDARD_32_64_BIT": "arm64 x86_64 i386", "ARCHS_STANDARD_32_BIT": "i386", "ARCHS_STANDARD_64_BIT": "arm64 x86_64", "ARCHS_STANDARD_INCLUDING_64_BIT": "arm64 x86_64", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "AUTOMATICALLY_MERGE_DEPENDENCIES": "NO", "AVAILABLE_PLATFORMS": "android appletvos appletvsimulator driverkit iphoneos iphonesimulator macosx qnx watchos watchsimulator xros xrsimulator", "BITCODE_GENERATION_MODE": "marker", "BUILD_ACTIVE_RESOURCES_ONLY": "NO", "BUILD_COMPONENTS": "headers build", "BUILD_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products", "BUILD_LIBRARY_FOR_DISTRIBUTION": "NO", "BUILD_ROOT": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products", "BUILD_STYLE": "", "BUILD_VARIANTS": "normal", "BUILT_PRODUCTS_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug", "BUNDLE_CONTENTS_FOLDER_PATH": "Contents/", "BUNDLE_CONTENTS_FOLDER_PATH_deep": "Contents/", "BUNDLE_EXECUTABLE_FOLDER_NAME_deep": "MacOS", "BUNDLE_EXECUTABLE_FOLDER_PATH": "Contents/MacOS", "BUNDLE_EXTENSIONS_FOLDER_PATH": "Contents/Extensions", "BUNDLE_FORMAT": "deep", "BUNDLE_FRAMEWORKS_FOLDER_PATH": "Contents/Frameworks", "BUNDLE_PLUGINS_FOLDER_PATH": "Contents/PlugIns", "BUNDLE_PRIVATE_HEADERS_FOLDER_PATH": "Contents/PrivateHeaders", "BUNDLE_PUBLIC_HEADERS_FOLDER_PATH": "Contents/Headers", "CACHE_ROOT": "/var/folders/74/mrn4sqgj5gx7tg68b4sdnsxc0000gn/C/com.apple.DeveloperTools/16.3-16E140/Xcode", "CCHROOT": "/var/folders/74/mrn4sqgj5gx7tg68b4sdnsxc0000gn/C/com.apple.DeveloperTools/16.3-16E140/Xcode", "CHMOD": "/bin/chmod", "CHOWN": "/usr/sbin/chown", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CACHE_FINE_GRAINED_OUTPUTS": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_EXPLICIT_MODULES": "YES", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_MODULES_BUILD_SESSION_FILE": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/ModuleCache.noindex/Session.modulevalidation", "CLANG_UNDEFINED_BEHAVIOR_SANITIZER_NULLABILITY": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_REPEATED_USE_OF_WEAK": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_PRAGMA_PACK": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "CLASS_FILE_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/JavaClasses", "CLEAN_PRECOMPS": "YES", "CLONE_HEADERS": "NO", "COCOAPODS_PARALLEL_CODE_SIGN": "true", "CODESIGNING_FOLDER_PATH": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_REQUIRED": "YES", "CODE_SIGN_IDENTITY": "-", "CODE_SIGN_IDENTITY_NO": "Apple Development", "CODE_SIGN_IDENTITY_YES": "-", "CODE_SIGN_INJECT_BASE_ENTITLEMENTS": "YES", "CODE_SIGN_STYLE": "Manual", "COLOR_DIAGNOSTICS": "NO", "COMBINE_HIDPI_IMAGES": "NO", "COMPILATION_CACHE_CAS_PATH": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/CompilationCache.noindex", "COMPILATION_CACHE_KEEP_CAS_DIRECTORY": "YES", "COMPILER_INDEX_STORE_ENABLE": "NO", "COMPOSITE_SDK_DIRS": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/CompositeSDKs", "COMPRESS_PNG_FILES": "NO", "CONFIGURATION": "Debug", "CONFIGURATION_BUILD_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug", "CONFIGURATION_TEMP_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug", "COPYING_PRESERVES_HFS_DATA": "NO", "COPY_HEADERS_RUN_UNIFDEF": "NO", "COPY_PHASE_STRIP": "NO", "CP": "/bin/cp", "CREATE_INFOPLIST_SECTION_IN_BINARY": "NO", "CURRENT_ARCH": "undefined_arch", "CURRENT_VARIANT": "normal", "DART_OBFUSCATION": "false", "DEAD_CODE_STRIPPING": "YES", "DEBUGGING_SYMBOLS": "YES", "DEBUG_INFORMATION_FORMAT": "dwarf", "DEBUG_INFORMATION_VERSION": "compiler-default", "DEFAULT_COMPILER": "com.apple.compilers.llvm.clang.1_0", "DEFAULT_DEXT_INSTALL_PATH": "/System/Library/DriverExtensions", "DEFAULT_KEXT_INSTALL_PATH": "/System/Library/Extensions", "DEFINES_MODULE": "NO", "DEPLOYMENT_LOCATION": "NO", "DEPLOYMENT_POSTPROCESSING": "NO", "DEPLOYMENT_TARGET_SETTING_NAME": "MACOSX_DEPLOYMENT_TARGET", "DEPLOYMENT_TARGET_SUGGESTED_VALUES": "10.13 10.14 10.15 11.0 11.1 11.2 11.3 11.4 11.5 12.0 12.2 12.3 12.4 13.0 13.1 13.2 13.3 13.4 13.5 14.0 14.1 14.2 14.3 14.4 14.5 14.6 15.0 15.1 15.2 15.3 15.4", "DERIVED_FILES_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/DerivedSources", "DERIVED_FILE_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/DerivedSources", "DERIVED_SOURCES_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/DerivedSources", "DEVELOPER_APPLICATIONS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications", "DEVELOPER_BIN_DIR": "/Applications/Xcode.app/Contents/Developer/usr/bin", "DEVELOPER_DIR": "/Applications/Xcode.app/Contents/Developer", "DEVELOPER_FRAMEWORKS_DIR": "/Applications/Xcode.app/Contents/Developer/Library/Frameworks", "DEVELOPER_FRAMEWORKS_DIR_QUOTED": "/Applications/Xcode.app/Contents/Developer/Library/Frameworks", "DEVELOPER_LIBRARY_DIR": "/Applications/Xcode.app/Contents/Developer/Library", "DEVELOPER_SDK_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs", "DEVELOPER_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Tools", "DEVELOPER_USR_DIR": "/Applications/Xcode.app/Contents/Developer/usr", "DEVELOPMENT_LANGUAGE": "en", "DIAGNOSE_MISSING_TARGET_DEPENDENCIES": "YES", "DIFF": "/usr/bin/diff", "DONT_GENERATE_INFOPLIST_FILE": "NO", "DSTROOT": "/tmp/Runner.dst", "DT_TOOLCHAIN_DIR": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "DWARF_DSYM_FILE_NAME": ".dSYM", "DWARF_DSYM_FILE_SHOULD_ACCOMPANY_PRODUCT": "NO", "DWARF_DSYM_FOLDER_PATH": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug", "DYNAMIC_LIBRARY_EXTENSION": "dylib", "EAGER_COMPILATION_ALLOW_SCRIPTS": "NO", "EAGER_LINKING": "NO", "EMBEDDED_CONTENT_CONTAINS_SWIFT": "NO", "EMBEDDED_PROFILE_NAME": "embedded.provisionprofile", "EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE": "NO", "ENABLE_APP_SANDBOX": "NO", "ENABLE_BITCODE": "NO", "ENABLE_CODE_COVERAGE": "YES", "ENABLE_DEBUG_DYLIB": "NO", "ENABLE_DEFAULT_HEADER_SEARCH_PATHS": "YES", "ENABLE_DEFAULT_SEARCH_PATHS": "YES", "ENABLE_HARDENED_RUNTIME": "NO", "ENABLE_HEADER_DEPENDENCIES": "YES", "ENABLE_INCOMING_NETWORK_CONNECTIONS": "NO", "ENABLE_ON_DEMAND_RESOURCES": "NO", "ENABLE_OUTGOING_NETWORK_CONNECTIONS": "NO", "ENABLE_PREVIEWS": "NO", "ENABLE_RESOURCE_ACCESS_AUDIO_INPUT": "NO", "ENABLE_RESOURCE_ACCESS_BLUETOOTH": "NO", "ENABLE_RESOURCE_ACCESS_CALENDARS": "NO", "ENABLE_RESOURCE_ACCESS_CAMERA": "NO", "ENABLE_RESOURCE_ACCESS_CONTACTS": "NO", "ENABLE_RESOURCE_ACCESS_LOCATION": "NO", "ENABLE_RESOURCE_ACCESS_PRINTING": "NO", "ENABLE_RESOURCE_ACCESS_USB": "NO", "ENABLE_SDK_IMPORTS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_TESTING_SEARCH_PATHS": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "ENABLE_XOJIT_PREVIEWS": "NO", "ENFORCE_VALID_ARCHS": "YES", "ENTITLEMENTS_DESTINATION": "Signature", "ENTITLEMENTS_REQUIRED": "YES", "EXCLUDED_INSTALLSRC_SUBDIRECTORY_PATTERNS": ".DS_Store .svn .git .hg CVS", "EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES": "*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj", "FILE_LIST": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/Objects/LinkFileList", "FIXED_FILES_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/FixedFiles", "FLUTTER_APPLICATION_PATH": "/Users/<USER>/Documents/Work/cursor/learnfocus", "FLUTTER_BUILD_DIR": "build", "FLUTTER_BUILD_NAME": "1.0.0", "FLUTTER_BUILD_NUMBER": "1", "FLUTTER_ROOT": "/Users/<USER>/development/flutter", "FLUTTER_TARGET": "/Users/<USER>/Documents/Work/cursor/learnfocus/lib/main.dart", "FRAMEWORK_VERSION": "A", "FUSE_BUILD_PHASES": "YES", "FUSE_BUILD_SCRIPT_PHASES": "NO", "GCC3_VERSION": "3.3", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PFE_FILE_C_DIALECTS": "c objective-c c++ objective-c++", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 ", "GCC_TREAT_WARNINGS_AS_ERRORS": "NO", "GCC_VERSION": "com.apple.compilers.llvm.clang.1_0", "GCC_VERSION_IDENTIFIER": "com_apple_compilers_llvm_clang_1_0", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_SHADOW": "YES", "GCC_WARN_STRICT_SELECTOR_MATCH": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "GENERATED_MODULEMAP_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/GeneratedModuleMaps", "GENERATE_INFOPLIST_FILE": "NO", "GENERATE_INTERMEDIATE_TEXT_BASED_STUBS": "YES", "GENERATE_MASTER_OBJECT_FILE": "NO", "GENERATE_PKGINFO_FILE": "NO", "GENERATE_PROFILING_CODE": "NO", "GENERATE_TEXT_BASED_STUBS": "NO", "GID": "20", "GROUP": "staff", "HEADERMAP_INCLUDES_FLAT_ENTRIES_FOR_TARGET_BEING_BUILT": "YES", "HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_ALL_PRODUCT_TYPES": "YES", "HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_TARGETS_NOT_BEING_BUILT": "YES", "HEADERMAP_INCLUDES_NONPUBLIC_NONPRIVATE_HEADERS": "YES", "HEADERMAP_INCLUDES_PROJECT_HEADERS": "YES", "HEADERMAP_USES_FRAMEWORK_PREFIX_ENTRIES": "YES", "HEADERMAP_USES_VFS": "NO", "HIDE_BITCODE_SYMBOLS": "YES", "HOME": "/Users/<USER>", "HOST_ARCH": "x86_64", "HOST_PLATFORM": "macosx", "ICONV": "/usr/bin/iconv", "IMPLICIT_DEPENDENCY_DOMAIN": "default", "INFOPLIST_ENABLE_CFBUNDLEICONS_MERGE": "YES", "INFOPLIST_EXPAND_BUILD_SETTINGS": "YES", "INFOPLIST_OUTPUT_FORMAT": "same-as-input", "INFOPLIST_PREPROCESS": "NO", "INLINE_PRIVATE_FRAMEWORKS": "NO", "INSTALLAPI_IGNORE_SKIP_INSTALL": "YES", "INSTALLHDRS_COPY_PHASE": "NO", "INSTALLHDRS_SCRIPT_PHASE": "NO", "INSTALL_DIR": "/tmp/Runner.dst", "INSTALL_GROUP": "staff", "INSTALL_MODE_FLAG": "u+w,go-w,a+rX", "INSTALL_OWNER": "govo", "INSTALL_ROOT": "/tmp/Runner.dst", "IOS_UNZIPPERED_TWIN_PREFIX_PATH": "/System/iOSSupport", "IS_MACCATALYST": "NO", "IS_UNOPTIMIZED_BUILD": "YES", "JAVAC_DEFAULT_FLAGS": "-J-Xms64m -J-XX:NewSize=4M -J-Dfile.encoding=UTF8", "JAVA_APP_STUB": "/System/Library/Frameworks/JavaVM.framework/Resources/MacOS/JavaApplicationStub", "JAVA_ARCHIVE_CLASSES": "YES", "JAVA_ARCHIVE_TYPE": "JAR", "JAVA_COMPILER": "/usr/bin/javac", "JAVA_FRAMEWORK_RESOURCES_DIRS": "Resources", "JAVA_JAR_FLAGS": "cv", "JAVA_SOURCE_SUBDIR": ".", "JAVA_USE_DEPENDENCIES": "YES", "JAVA_ZIP_FLAGS": "-urg", "JIKES_DEFAULT_FLAGS": "+E +OLDCSO", "KASAN_CFLAGS_CLASSIC": "-DKASAN=1 -DKASAN_CLASSIC=1 -fsanitize=address -mllvm -asan-globals-live-support -mllvm -asan-force-dynamic-shadow", "KASAN_CFLAGS_TBI": "-DKASAN=1 -DKASAN_TBI=1 -fsanitize=kernel-hwaddress -mllvm -hwasan-recover=0 -mllvm -hwasan-instrument-atomics=0 -mllvm -hwasan-instrument-stack=1 -mllvm -hwasan-generate-tags-with-calls=1 -mllvm -hwasan-instrument-with-calls=1 -mllvm -hwasan-use-short-granules=0 -mllvm -hwasan-memory-access-callback-prefix=__asan_", "KASAN_DEFAULT_CFLAGS": "-DKASAN=1 -DKASAN_CLASSIC=1 -fsanitize=address -mllvm -asan-globals-live-support -mllvm -asan-force-dynamic-shadow", "KEEP_PRIVATE_EXTERNS": "NO", "LD_DEPENDENCY_INFO_FILE": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/Objects-normal/undefined_arch/Flutter Assemble_dependency_info.dat", "LD_EXPORT_SYMBOLS": "YES", "LD_GENERATE_MAP_FILE": "NO", "LD_MAP_FILE_PATH": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/Flutter Assemble-LinkMap-normal-undefined_arch.txt", "LD_NO_PIE": "NO", "LD_QUOTE_LINKER_ARGUMENTS_FOR_COMPILER_DRIVER": "YES", "LD_SHARED_CACHE_ELIGIBLE": "Automatic", "LD_WARN_DUPLICATE_LIBRARIES": "NO", "LD_WARN_UNUSED_DYLIBS": "NO", "LEGACY_DEVELOPER_DIR": "/Applications/Xcode.app/Contents/PlugIns/Xcode3Core.ideplugin/Contents/SharedSupport/Developer", "LEX": "lex", "LIBRARY_DEXT_INSTALL_PATH": "/Library/DriverExtensions", "LIBRARY_FLAG_NOSPACE": "YES", "LIBRARY_KEXT_INSTALL_PATH": "/Library/Extensions", "LINKER_DISPLAYS_MANGLED_NAMES": "NO", "LINK_FILE_LIST_normal_x86_64": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/Objects-normal/x86_64/Flutter Assemble.LinkFileList", "LINK_OBJC_RUNTIME": "YES", "LINK_WITH_STANDARD_LIBRARIES": "YES", "LLVM_TARGET_TRIPLE_OS_VERSION": "macos10.14", "LLVM_TARGET_TRIPLE_OS_VERSION_NO": "macos10.14", "LLVM_TARGET_TRIPLE_OS_VERSION_YES": "macos15.4", "LLVM_TARGET_TRIPLE_VENDOR": "apple", "LM_AUX_CONST_METADATA_LIST_PATH_normal_x86_64": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/Objects-normal/x86_64/Flutter Assemble.SwiftConstValuesFileList", "LOCALIZATION_EXPORT_SUPPORTED": "YES", "LOCALIZATION_PREFERS_STRING_CATALOGS": "NO", "LOCALIZED_STRING_MACRO_NAMES": "NSLocalizedString CFCopyLocalizedString", "LOCALIZED_STRING_SWIFTUI_SUPPORT": "YES", "LOCAL_ADMIN_APPS_DIR": "/Applications/Utilities", "LOCAL_APPS_DIR": "/Applications", "LOCAL_DEVELOPER_DIR": "/Library/Developer", "LOCAL_LIBRARY_DIR": "/Library", "LOCROOT": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos", "LOCSYMROOT": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos", "MACOSX_DEPLOYMENT_TARGET": "10.14", "MAC_OS_X_PRODUCT_BUILD_VERSION": "24F74", "MAC_OS_X_VERSION_ACTUAL": "150500", "MAC_OS_X_VERSION_MAJOR": "150000", "MAC_OS_X_VERSION_MINOR": "150500", "MAKE_MERGEABLE": "NO", "MERGEABLE_LIBRARY": "NO", "MERGED_BINARY_TYPE": "none", "MERGE_LINKED_LIBRARIES": "NO", "METAL_LIBRARY_FILE_BASE": "default", "METAL_LIBRARY_OUTPUT_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/", "MODULE_CACHE_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/ModuleCache.noindex", "MTL_ENABLE_DEBUG_INFO": "YES", "NATIVE_ARCH": "x86_64", "NATIVE_ARCH_32_BIT": "i386", "NATIVE_ARCH_64_BIT": "x86_64", "NATIVE_ARCH_ACTUAL": "x86_64", "NO_COMMON": "YES", "OBJECT_FILE_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/Objects", "OBJECT_FILE_DIR_normal": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/Objects-normal", "OBJROOT": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex", "ONLY_ACTIVE_ARCH": "YES", "OS": "MACOS", "OSAC": "/usr/bin/osacompile", "PACKAGE_CONFIG": "/Users/<USER>/Documents/Work/cursor/learnfocus/.dart_tool/package_config.json", "PASCAL_STRINGS": "YES", "PATH": "/Applications/Xcode.app/Contents/SharedFrameworks/SwiftBuild.framework/Versions/A/PlugIns/SWBBuildService.bundle/Contents/PlugIns/SWBUniversalPlatformPlugin.bundle/Contents/Frameworks/SWBUniversalPlatform.framework/Resources:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/Users/<USER>/.trae/sdks/workspaces/0241d5d4/versions/node/current/bin:/Users/<USER>/.trae/sdks/versions/node/current/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/.pyenv/shims:/Users/<USER>/.wasmtime/bin:/Users/<USER>/Documents/Work/ffmpeg:/usr/local/opt/openssl@1.1/bin:/Users/<USER>/Library/Android/sdk/platform-tools:/Users/<USER>/.nvm/versions/node/v20.18.1/bin:/Users/<USER>/development/flutter/bin:/Library/Frameworks/Python.framework/Versions/3.9/bin:/Library/Frameworks/Python.framework/Versions/3.11/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Applications/Wireshark.app/Contents/MacOS:/Applications/VMware Fusion.app/Contents/Public:/usr/local/go/bin:/Applications/Trae.app/Contents/Resources/app/bin:/Users/<USER>/Library/pnpm:/opt/miniconda3/bin:/opt/miniconda3/condabin:/Users/<USER>/.cargo/bin:/Users/<USER>/.rvm/bin:/Users/<USER>/Library/Android/sdk/emulator:/Users/<USER>/Library/Android/sdk/tools:/Users/<USER>/Library/Android/sdk/tools/bin:/usr/local/mysql/bin:/Users/<USER>/Documents/OpenHarmony/11/toolchains", "PATH_PREFIXES_EXCLUDED_FROM_HEADER_DEPENDENCIES": "/usr/include /usr/local/include /System/Library/Frameworks /System/Library/PrivateFrameworks /Applications/Xcode.app/Contents/Developer/Headers /Applications/Xcode.app/Contents/Developer/SDKs /Applications/Xcode.app/Contents/Developer/Platforms", "PER_ARCH_MODULE_FILE_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/Objects-normal/undefined_arch", "PER_ARCH_OBJECT_FILE_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/Objects-normal/undefined_arch", "PER_VARIANT_OBJECT_FILE_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/Objects-normal", "PKGINFO_FILE_PATH": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/PkgInfo", "PLATFORM_DEVELOPER_APPLICATIONS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications", "PLATFORM_DEVELOPER_BIN_DIR": "/Applications/Xcode.app/Contents/Developer/usr/bin", "PLATFORM_DEVELOPER_LIBRARY_DIR": "/Applications/Xcode.app/Contents/Developer/Library", "PLATFORM_DEVELOPER_SDK_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs", "PLATFORM_DEVELOPER_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Tools", "PLATFORM_DEVELOPER_USR_DIR": "/Applications/Xcode.app/Contents/Developer/usr", "PLATFORM_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform", "PLATFORM_DISPLAY_NAME": "macOS", "PLATFORM_FAMILY_NAME": "macOS", "PLATFORM_NAME": "macosx", "PLATFORM_PREFERRED_ARCH": "x86_64", "PLATFORM_PRODUCT_BUILD_VERSION": "24E241", "PLATFORM_REQUIRES_SWIFT_AUTOLINK_EXTRACT": "NO", "PLATFORM_REQUIRES_SWIFT_MODULEWRAP": "NO", "PLIST_FILE_OUTPUT_FORMAT": "same-as-input", "PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR": "YES", "PRECOMP_DESTINATION_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/PrefixHeaders", "PROCESSED_INFOPLIST_PATH": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/Objects-normal/undefined_arch/Processed-Info.plist", "PRODUCT_MODULE_NAME": "Flutter_Assemble", "PRODUCT_NAME": "Flutter Assemble", "PRODUCT_SETTINGS_PATH": "", "PROFILING_CODE": "NO", "PROJECT": "Runner", "PROJECT_DERIVED_FILE_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/DerivedSources", "PROJECT_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos", "PROJECT_FILE_PATH": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner.xcodeproj", "PROJECT_GUID": "18c1723432283e0cc55f10a6dcfd9e02", "PROJECT_NAME": "Runner", "PROJECT_TEMP_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build", "PROJECT_TEMP_ROOT": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex", "RECOMMENDED_MACOSX_DEPLOYMENT_TARGET": "11.0", "RECURSIVE_SEARCH_PATHS_FOLLOW_SYMLINKS": "YES", "REMOVE_CVS_FROM_RESOURCES": "YES", "REMOVE_GIT_FROM_RESOURCES": "YES", "REMOVE_HEADERS_FROM_EMBEDDED_BUNDLES": "YES", "REMOVE_HG_FROM_RESOURCES": "YES", "REMOVE_STATIC_EXECUTABLES_FROM_EMBEDDED_BUNDLES": "YES", "REMOVE_SVN_FROM_RESOURCES": "YES", "RESCHEDULE_INDEPENDENT_HEADERS_PHASES": "YES", "REZ_COLLECTOR_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/ResourceManagerResources", "REZ_OBJECTS_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/ResourceManagerResources/Objects", "SCAN_ALL_SOURCE_FILES_FOR_INCLUDES": "NO", "SCRIPT_INPUT_FILE_0": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Flutter/ephemeral/tripwire", "SCRIPT_INPUT_FILE_COUNT": "1", "SCRIPT_INPUT_FILE_LIST_0": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/InputFileList-33CC111E2044C6BF0003C045-FlutterInputs-d057753841b7976b70fc76b956a78b2a-resolved.xcfilelist", "SCRIPT_INPUT_FILE_LIST_COUNT": "1", "SCRIPT_OUTPUT_FILE_COUNT": "0", "SCRIPT_OUTPUT_FILE_LIST_0": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/OutputFileList-33CC111E2044C6BF0003C045-FlutterOutputs-36263e2a36d06ad58f39863a7bf8a614-resolved.xcfilelist", "SCRIPT_OUTPUT_FILE_LIST_COUNT": "1", "SDKROOT": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "SDK_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "SDK_DIR_macosx": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "SDK_DIR_macosx15_4": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "SDK_NAME": "macosx15.4", "SDK_NAMES": "macosx15.4", "SDK_PRODUCT_BUILD_VERSION": "24E241", "SDK_STAT_CACHE_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos", "SDK_STAT_CACHE_ENABLE": "YES", "SDK_STAT_CACHE_PATH": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/SDKStatCaches.noindex/macosx15.4-24E241-88b860576fb364319593bd8fb30666b0.sdkstatcache", "SDK_VERSION": "15.4", "SDK_VERSION_ACTUAL": "150400", "SDK_VERSION_MAJOR": "150000", "SDK_VERSION_MINOR": "150400", "SED": "/usr/bin/sed", "SEPARATE_STRIP": "NO", "SEPARATE_SYMBOL_EDIT": "NO", "SET_DIR_MODE_OWNER_GROUP": "YES", "SET_FILE_MODE_OWNER_GROUP": "NO", "SHALLOW_BUNDLE": "NO", "SHARED_DERIVED_FILE_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/DerivedSources", "SHARED_PRECOMPS_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/PrecompiledHeaders", "SKIP_INSTALL": "YES", "SOURCE_ROOT": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos", "SRCROOT": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos", "STRINGSDATA_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/Objects-normal/undefined_arch", "STRINGSDATA_ROOT": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build", "STRINGS_FILE_INFOPLIST_RENAME": "YES", "STRINGS_FILE_OUTPUT_ENCODING": "UTF-16", "STRIP_BITCODE_FROM_COPIED_FILES": "NO", "STRIP_INSTALLED_PRODUCT": "NO", "STRIP_PNG_TEXT": "NO", "STRIP_STYLE": "all", "STRIP_SWIFT_SYMBOLS": "YES", "SUPPORTED_PLATFORMS": "macosx", "SUPPORTS_TEXT_BASED_API": "NO", "SUPPRESS_WARNINGS": "NO", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_EMIT_LOC_STRINGS": "NO", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_PLATFORM_TARGET_PREFIX": "macos", "SWIFT_RESPONSE_FILE_PATH_normal_x86_64": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/Objects-normal/x86_64/Flutter Assemble.SwiftFileList", "SYMROOT": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products", "SYSTEM_ADMIN_APPS_DIR": "/Applications/Utilities", "SYSTEM_APPS_DIR": "/Applications", "SYSTEM_CORE_SERVICES_DIR": "/System/Library/CoreServices", "SYSTEM_DEMOS_DIR": "/Applications/Extras", "SYSTEM_DEVELOPER_APPS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications", "SYSTEM_DEVELOPER_BIN_DIR": "/Applications/Xcode.app/Contents/Developer/usr/bin", "SYSTEM_DEVELOPER_DEMOS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Utilities/Built Examples", "SYSTEM_DEVELOPER_DIR": "/Applications/Xcode.app/Contents/Developer", "SYSTEM_DEVELOPER_DOC_DIR": "/Applications/Xcode.app/Contents/Developer/ADC Reference Library", "SYSTEM_DEVELOPER_GRAPHICS_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Graphics Tools", "SYSTEM_DEVELOPER_JAVA_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Java Tools", "SYSTEM_DEVELOPER_PERFORMANCE_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Performance Tools", "SYSTEM_DEVELOPER_RELEASENOTES_DIR": "/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes", "SYSTEM_DEVELOPER_TOOLS": "/Applications/Xcode.app/Contents/Developer/Tools", "SYSTEM_DEVELOPER_TOOLS_DOC_DIR": "/Applications/Xcode.app/Contents/Developer/ADC Reference Library/documentation/DeveloperTools", "SYSTEM_DEVELOPER_TOOLS_RELEASENOTES_DIR": "/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes/DeveloperTools", "SYSTEM_DEVELOPER_USR_DIR": "/Applications/Xcode.app/Contents/Developer/usr", "SYSTEM_DEVELOPER_UTILITIES_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Utilities", "SYSTEM_DEXT_INSTALL_PATH": "/System/Library/DriverExtensions", "SYSTEM_DOCUMENTATION_DIR": "/Library/Documentation", "SYSTEM_KEXT_INSTALL_PATH": "/System/Library/Extensions", "SYSTEM_LIBRARY_DIR": "/System/Library", "TAPI_DEMANGLE": "YES", "TAPI_ENABLE_PROJECT_HEADERS": "NO", "TAPI_LANGUAGE": "objective-c", "TAPI_LANGUAGE_STANDARD": "compiler-default", "TAPI_USE_SRCROOT": "YES", "TAPI_VERIFY_MODE": "Pedantic", "TARGETNAME": "Flutter Assemble", "TARGET_BUILD_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug", "TARGET_NAME": "Flutter Assemble", "TARGET_TEMP_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build", "TEMP_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build", "TEMP_FILES_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build", "TEMP_FILE_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build", "TEMP_ROOT": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex", "TEMP_SANDBOX_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/TemporaryTaskSandboxes", "TEST_FRAMEWORK_SEARCH_PATHS": " /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "TEST_LIBRARY_SEARCH_PATHS": " /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "TOOLCHAINS": "com.apple.dt.toolchain.XcodeDefault", "TOOLCHAIN_DIR": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "TRACK_WIDGET_CREATION": "true", "TREAT_MISSING_BASELINES_AS_TEST_FAILURES": "NO", "TREAT_MISSING_SCRIPT_PHASE_OUTPUTS_AS_ERRORS": "NO", "TREE_SHAKE_ICONS": "false", "UID": "501", "UNINSTALLED_PRODUCTS_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/UninstalledProducts", "UNSTRIPPED_PRODUCT": "NO", "USER": "govo", "USER_APPS_DIR": "/Users/<USER>/Applications", "USER_LIBRARY_DIR": "/Users/<USER>/Library", "USE_DYNAMIC_NO_PIC": "YES", "USE_HEADERMAP": "YES", "USE_HEADER_SYMLINKS": "NO", "VALIDATE_DEVELOPMENT_ASSET_PATHS": "YES_ERROR", "VALIDATE_PRODUCT": "NO", "VALID_ARCHS": "arm64 arm64e i386 x86_64", "VERBOSE_PBXCP": "NO", "VERSION_INFO_BUILDER": "govo", "VERSION_INFO_FILE": "Flutter Assemble_vers.c", "VERSION_INFO_STRING": "\"@(#)PROGRAM:Flutter Assemble  PROJECT:Runner-\"", "WARNING_CFLAGS": "-Wall -Wconditional-uninitialized -Wnullable-to-nonnull-conversion -Wmissing-method-return-type -Woverlength-strings", "WORKSPACE_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos", "WRAP_ASSET_PACKS_IN_SEPARATE_DIRECTORIES": "NO", "XCODE_APP_SUPPORT_DIR": "/Applications/Xcode.app/Contents/Developer/Library/Xcode", "XCODE_PRODUCT_BUILD_VERSION": "16E140", "XCODE_VERSION_ACTUAL": "1630", "XCODE_VERSION_MAJOR": "1600", "XCODE_VERSION_MINOR": "1630", "XPCSERVICES_FOLDER_PATH": "/XPCServices", "YACC": "yacc", "_BOOL_": "NO", "_BOOL_NO": "NO", "_BOOL_YES": "YES", "_DEVELOPMENT_TEAM_IS_EMPTY": "YES", "_DISCOVER_COMMAND_LINE_LINKER_INPUTS": "YES", "_DISCOVER_COMMAND_LINE_LINKER_INPUTS_INCLUDE_WL": "YES", "_IS_EMPTY_": "YES", "_MACOSX_DEPLOYMENT_TARGET_IS_EMPTY": "NO", "__DIAGNOSE_DEPRECATED_ARCHS": "YES", "arch": "undefined_arch", "variant": "normal"}, "allow-missing-inputs": true, "always-out-of-date": true, "working-directory": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos", "control-enabled": false, "repair-via-ownership-analysis": true, "signature": "2746f32385229711962b7c1ecc8165ae"}, "P2:target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/InputFileList-33CC111E2044C6BF0003C045-FlutterInputs-d057753841b7976b70fc76b956a78b2a-resolved.xcfilelist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/InputFileList-33CC111E2044C6BF0003C045-FlutterInputs-d057753841b7976b70fc76b956a78b2a-resolved.xcfilelist", "inputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--immediate>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/InputFileList-33CC111E2044C6BF0003C045-FlutterInputs-d057753841b7976b70fc76b956a78b2a-resolved.xcfilelist"]}, "P2:target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/OutputFileList-33CC111E2044C6BF0003C045-FlutterOutputs-36263e2a36d06ad58f39863a7bf8a614-resolved.xcfilelist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/OutputFileList-33CC111E2044C6BF0003C045-FlutterOutputs-36263e2a36d06ad58f39863a7bf8a614-resolved.xcfilelist", "inputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--immediate>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/OutputFileList-33CC111E2044C6BF0003C045-FlutterOutputs-36263e2a36d06ad58f39863a7bf8a614-resolved.xcfilelist"]}, "P2:target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/Script-33CC111E2044C6BF0003C045.sh": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/Script-33CC111E2044C6BF0003C045.sh", "inputs": ["<target-Flutter Assemble-18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a--immediate>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Flutter Assemble.build/Script-33CC111E2044C6BF0003C045.sh"]}, "P2:target-Runner-****************************************************************-:Debug:ConstructStubExecutorLinkFileList /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-ExecutorLinkFileList-normal-x86_64.txt": {"tool": "construct-stub-executor-input-file-list", "description": "ConstructStubExecutorLinkFileList /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-ExecutorLinkFileList-normal-x86_64.txt", "inputs": ["/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib/libPreviewsJITStubExecutor_no_swift_entry_point.a", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib/libPreviewsJITStubExecutor.a", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus.debug.dylib", "<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-ExecutorLinkFileList-normal-x86_64.txt"]}, "P2:target-Runner-****************************************************************-:Debug:Copy /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftsourceinfo": {"tool": "file-copy", "description": "Copy /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftsourceinfo", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftsourceinfo/", "<target-Runner-****************************************************************--copy-headers-completion>", "<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo"]}, "P2:target-Runner-****************************************************************-:Debug:Copy /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/x86_64-apple-macos.abi.json /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/x86_64-apple-macos.abi.json /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.abi.json", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.abi.json/", "<target-Runner-****************************************************************--copy-headers-completion>", "<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/x86_64-apple-macos.abi.json"]}, "P2:target-Runner-****************************************************************-:Debug:Copy /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/x86_64-apple-macos.swiftdoc /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/x86_64-apple-macos.swiftdoc /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftdoc", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftdoc/", "<target-Runner-****************************************************************--copy-headers-completion>", "<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/x86_64-apple-macos.swiftdoc"]}, "P2:target-Runner-****************************************************************-:Debug:Copy /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/x86_64-apple-macos.swiftmodule /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/x86_64-apple-macos.swiftmodule /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftmodule", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftmodule/", "<target-Runner-****************************************************************--copy-headers-completion>", "<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.swiftmodule/x86_64-apple-macos.swiftmodule"]}, "P2:target-Runner-****************************************************************-:Debug:Ld /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/__preview.dylib normal": {"tool": "shell", "description": "Ld /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/__preview.dylib normal", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/__preview.dylib", "<Linked Binary Preview Injection Dylib /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/__preview.dylib>", "<TRIGGER: Ld /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/__preview.dylib normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "x86_64-apple-macos10.14", "-dynamiclib", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-O0", "-L/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug", "-F/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug", "-install_name", "@rpath/learnfocus.debug.dylib", "-dead_strip", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus_dependency_info.dat", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/__preview.dylib"], "env": {}, "working-directory": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos", "signature": "fb0bbb72a7c2bfbcd1b6710940e6d792"}, "P2:target-Runner-****************************************************************-:Debug:Ld /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus normal": {"tool": "shell", "description": "Ld /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus normal", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus.debug.dylib", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-DebugDylibPath-normal-x86_64.txt", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-DebugDylibInstallName-normal-x86_64.txt", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-ExecutorLinkFileList-normal-x86_64.txt", "<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus", "<Linked Binary /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus>", "<TRIGGER: Ld /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "x86_64-apple-macos10.14", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-O0", "-L/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug", "-F/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/../Frameworks", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-e", "___debug_blank_executor_main", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__debug_dylib", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-DebugDylibPath-normal-x86_64.txt", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__debug_instlnm", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-DebugDylibInstallName-normal-x86_64.txt", "-<PERSON><PERSON><PERSON>", "-filelist", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-ExecutorLinkFileList-normal-x86_64.txt", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus.debug.dylib", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus"], "env": {}, "working-directory": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos", "signature": "734ce790db26ef9e069709c1b7427fc1"}, "P2:target-Runner-****************************************************************-:Debug:Ld /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus.debug.dylib normal": {"tool": "shell", "description": "Ld /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus.debug.dylib normal", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/MainFlutterWindow.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.LinkFileList", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug", "<target-Runner-****************************************************************--generated-headers>", "<target-Runner-****************************************************************--swift-generated-headers>", "<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus.debug.dylib", "<Linked Binary Debug Dylib /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus.debug.dylib>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus_lto.o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus_dependency_info.dat", "<TRIGGER: Ld /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus.debug.dylib normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "x86_64-apple-macos10.14", "-dynamiclib", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-O0", "-L/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/EagerLinkingTBDs/Debug", "-L/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug", "-F/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/EagerLinkingTBDs/Debug", "-F/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug", "-filelist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.LinkFileList", "-install_name", "@rpath/learnfocus.debug.dylib", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/../Frameworks", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus_lto.o", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus_dependency_info.dat", "-fobjc-link-runtime", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftmodule", "-<PERSON><PERSON><PERSON>", "-alias", "-<PERSON><PERSON><PERSON>", "_main", "-<PERSON><PERSON><PERSON>", "___debug_main_executable_dylib_entry_point", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/MacOS/learnfocus.debug.dylib"], "env": {}, "working-directory": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos", "deps": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus_dependency_info.dat"], "deps-style": "dependency-info", "signature": "4c0b1979c38e02b18aeb10459bdb93c2"}, "P2:target-Runner-****************************************************************-:Debug:PhaseScriptExecution Run Script /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Script-3399D490228B24CF009A79C7.sh": {"tool": "shell", "description": "PhaseScriptExecution Run Script /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Script-3399D490228B24CF009A79C7.sh", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Script-3399D490228B24CF009A79C7.sh", "<target-Runner-****************************************************************--fused-phase1-copy-files>", "<target-Runner-****************************************************************--entry>"], "outputs": ["<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e0280fce6e78b27fa69321adec3779466d5-target-Runner-****************************************************************->"], "args": ["/bin/sh", "-c", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Script-3399D490228B24CF009A79C7.sh"], "env": {"ACTION": "build", "AD_HOC_CODE_SIGNING_ALLOWED": "YES", "AGGREGATE_TRACKED_DOMAINS": "YES", "ALLOW_BUILD_REQUEST_OVERRIDES": "NO", "ALLOW_TARGET_PLATFORM_SPECIALIZATION": "NO", "ALTERNATE_GROUP": "staff", "ALTERNATE_MODE": "u+w,go-w,a+rX", "ALTERNATE_OWNER": "govo", "ALTERNATIVE_DISTRIBUTION_WEB": "NO", "ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES": "NO", "ALWAYS_SEARCH_USER_PATHS": "NO", "ALWAYS_USE_SEPARATE_HEADERMAPS": "NO", "APPLICATION_EXTENSION_API_ONLY": "NO", "APPLY_RULES_IN_COPY_FILES": "NO", "APPLY_RULES_IN_COPY_HEADERS": "NO", "APP_SHORTCUTS_ENABLE_FLEXIBLE_MATCHING": "YES", "ARCHS": "x86_64", "ARCHS_STANDARD": "arm64 x86_64", "ARCHS_STANDARD_32_64_BIT": "arm64 x86_64 i386", "ARCHS_STANDARD_32_BIT": "i386", "ARCHS_STANDARD_64_BIT": "arm64 x86_64", "ARCHS_STANDARD_INCLUDING_64_BIT": "arm64 x86_64", "ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "AUTOMATICALLY_MERGE_DEPENDENCIES": "NO", "AVAILABLE_PLATFORMS": "android appletvos appletvsimulator driverkit iphoneos iphonesimulator macosx qnx watchos watchsimulator xros xrsimulator", "BITCODE_GENERATION_MODE": "marker", "BUILD_ACTIVE_RESOURCES_ONLY": "NO", "BUILD_COMPONENTS": "headers build", "BUILD_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products", "BUILD_LIBRARY_FOR_DISTRIBUTION": "NO", "BUILD_ROOT": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products", "BUILD_STYLE": "", "BUILD_VARIANTS": "normal", "BUILT_PRODUCTS_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug", "BUNDLE_CONTENTS_FOLDER_PATH": "Contents/", "BUNDLE_CONTENTS_FOLDER_PATH_deep": "Contents/", "BUNDLE_EXECUTABLE_FOLDER_NAME_deep": "MacOS", "BUNDLE_EXECUTABLE_FOLDER_PATH": "Contents/MacOS", "BUNDLE_EXTENSIONS_FOLDER_PATH": "Contents/Extensions", "BUNDLE_FORMAT": "deep", "BUNDLE_FRAMEWORKS_FOLDER_PATH": "Contents/Frameworks", "BUNDLE_PLUGINS_FOLDER_PATH": "Contents/PlugIns", "BUNDLE_PRIVATE_HEADERS_FOLDER_PATH": "Contents/PrivateHeaders", "BUNDLE_PUBLIC_HEADERS_FOLDER_PATH": "Contents/Headers", "CACHE_ROOT": "/var/folders/74/mrn4sqgj5gx7tg68b4sdnsxc0000gn/C/com.apple.DeveloperTools/16.3-16E140/Xcode", "CCHROOT": "/var/folders/74/mrn4sqgj5gx7tg68b4sdnsxc0000gn/C/com.apple.DeveloperTools/16.3-16E140/Xcode", "CHMOD": "/bin/chmod", "CHOWN": "/usr/sbin/chown", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CACHE_FINE_GRAINED_OUTPUTS": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_EXPLICIT_MODULES": "YES", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_MODULES_BUILD_SESSION_FILE": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/ModuleCache.noindex/Session.modulevalidation", "CLANG_UNDEFINED_BEHAVIOR_SANITIZER_NULLABILITY": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_REPEATED_USE_OF_WEAK": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_PRAGMA_PACK": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "CLASS_FILE_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/JavaClasses", "CLEAN_PRECOMPS": "YES", "CLONE_HEADERS": "NO", "COCOAPODS_PARALLEL_CODE_SIGN": "true", "CODESIGNING_FOLDER_PATH": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app", "CODE_SIGNING_ALLOWED": "YES", "CODE_SIGNING_REQUIRED": "YES", "CODE_SIGN_ENTITLEMENTS": "Runner/DebugProfile.entitlements", "CODE_SIGN_IDENTITY": "-", "CODE_SIGN_IDENTITY_NO": "Apple Development", "CODE_SIGN_IDENTITY_YES": "-", "CODE_SIGN_INJECT_BASE_ENTITLEMENTS": "YES", "CODE_SIGN_STYLE": "Automatic", "COLOR_DIAGNOSTICS": "NO", "COMBINE_HIDPI_IMAGES": "YES", "COMPILATION_CACHE_CAS_PATH": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/CompilationCache.noindex", "COMPILATION_CACHE_KEEP_CAS_DIRECTORY": "YES", "COMPILER_INDEX_STORE_ENABLE": "NO", "COMPOSITE_SDK_DIRS": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/CompositeSDKs", "COMPRESS_PNG_FILES": "NO", "CONFIGURATION": "Debug", "CONFIGURATION_BUILD_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug", "CONFIGURATION_TEMP_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug", "CONTENTS_FOLDER_PATH": "learnfocus.app/Contents", "CONTENTS_FOLDER_PATH_SHALLOW_BUNDLE_NO": "learnfocus.app/Contents", "CONTENTS_FOLDER_PATH_SHALLOW_BUNDLE_YES": "learnfocus.app", "COPYING_PRESERVES_HFS_DATA": "NO", "COPY_HEADERS_RUN_UNIFDEF": "NO", "COPY_PHASE_STRIP": "NO", "CP": "/bin/cp", "CREATE_INFOPLIST_SECTION_IN_BINARY": "NO", "CURRENT_ARCH": "undefined_arch", "CURRENT_VARIANT": "normal", "DART_OBFUSCATION": "false", "DEAD_CODE_STRIPPING": "YES", "DEBUGGING_SYMBOLS": "YES", "DEBUG_INFORMATION_FORMAT": "dwarf", "DEBUG_INFORMATION_VERSION": "compiler-default", "DEFAULT_COMPILER": "com.apple.compilers.llvm.clang.1_0", "DEFAULT_DEXT_INSTALL_PATH": "/System/Library/DriverExtensions", "DEFAULT_KEXT_INSTALL_PATH": "/System/Library/Extensions", "DEFINES_MODULE": "NO", "DEPLOYMENT_LOCATION": "NO", "DEPLOYMENT_POSTPROCESSING": "NO", "DEPLOYMENT_TARGET_SETTING_NAME": "MACOSX_DEPLOYMENT_TARGET", "DEPLOYMENT_TARGET_SUGGESTED_VALUES": "10.13 10.14 10.15 11.0 11.1 11.2 11.3 11.4 11.5 12.0 12.2 12.3 12.4 13.0 13.1 13.2 13.3 13.4 13.5 14.0 14.1 14.2 14.3 14.4 14.5 14.6 15.0 15.1 15.2 15.3 15.4", "DERIVED_FILES_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources", "DERIVED_FILE_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources", "DERIVED_SOURCES_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources", "DEVELOPER_APPLICATIONS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications", "DEVELOPER_BIN_DIR": "/Applications/Xcode.app/Contents/Developer/usr/bin", "DEVELOPER_DIR": "/Applications/Xcode.app/Contents/Developer", "DEVELOPER_FRAMEWORKS_DIR": "/Applications/Xcode.app/Contents/Developer/Library/Frameworks", "DEVELOPER_FRAMEWORKS_DIR_QUOTED": "/Applications/Xcode.app/Contents/Developer/Library/Frameworks", "DEVELOPER_LIBRARY_DIR": "/Applications/Xcode.app/Contents/Developer/Library", "DEVELOPER_SDK_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs", "DEVELOPER_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Tools", "DEVELOPER_USR_DIR": "/Applications/Xcode.app/Contents/Developer/usr", "DEVELOPMENT_LANGUAGE": "en", "DIAGNOSE_MISSING_TARGET_DEPENDENCIES": "YES", "DIFF": "/usr/bin/diff", "DOCUMENTATION_FOLDER_PATH": "learnfocus.app/Contents/Resources/en.lproj/Documentation", "DONT_GENERATE_INFOPLIST_FILE": "NO", "DSTROOT": "/tmp/Runner.dst", "DT_TOOLCHAIN_DIR": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "DWARF_DSYM_FILE_NAME": "learnfocus.app.dSYM", "DWARF_DSYM_FILE_SHOULD_ACCOMPANY_PRODUCT": "NO", "DWARF_DSYM_FOLDER_PATH": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug", "DYNAMIC_LIBRARY_EXTENSION": "dylib", "EAGER_COMPILATION_ALLOW_SCRIPTS": "NO", "EAGER_LINKING": "NO", "EMBEDDED_CONTENT_CONTAINS_SWIFT": "NO", "EMBEDDED_PROFILE_NAME": "embedded.provisionprofile", "EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE": "NO", "ENABLE_APP_SANDBOX": "NO", "ENABLE_BITCODE": "NO", "ENABLE_CODE_COVERAGE": "YES", "ENABLE_DEBUG_DYLIB": "YES", "ENABLE_DEFAULT_HEADER_SEARCH_PATHS": "YES", "ENABLE_DEFAULT_SEARCH_PATHS": "YES", "ENABLE_HARDENED_RUNTIME": "NO", "ENABLE_HEADER_DEPENDENCIES": "YES", "ENABLE_INCOMING_NETWORK_CONNECTIONS": "NO", "ENABLE_ON_DEMAND_RESOURCES": "NO", "ENABLE_OUTGOING_NETWORK_CONNECTIONS": "NO", "ENABLE_PREVIEWS": "NO", "ENABLE_RESOURCE_ACCESS_AUDIO_INPUT": "NO", "ENABLE_RESOURCE_ACCESS_BLUETOOTH": "NO", "ENABLE_RESOURCE_ACCESS_CALENDARS": "NO", "ENABLE_RESOURCE_ACCESS_CAMERA": "NO", "ENABLE_RESOURCE_ACCESS_CONTACTS": "NO", "ENABLE_RESOURCE_ACCESS_LOCATION": "NO", "ENABLE_RESOURCE_ACCESS_PRINTING": "NO", "ENABLE_RESOURCE_ACCESS_USB": "NO", "ENABLE_SDK_IMPORTS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_TESTING_SEARCH_PATHS": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "ENABLE_XOJIT_PREVIEWS": "YES", "ENFORCE_VALID_ARCHS": "YES", "ENTITLEMENTS_ALLOWED": "YES", "ENTITLEMENTS_DESTINATION": "Signature", "ENTITLEMENTS_REQUIRED": "NO", "EXCLUDED_INSTALLSRC_SUBDIRECTORY_PATTERNS": ".DS_Store .svn .git .hg CVS", "EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES": "*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj", "EXECUTABLES_FOLDER_PATH": "learnfocus.app/Contents/Executables", "EXECUTABLE_BLANK_INJECTION_DYLIB_PATH": "learnfocus.app/Contents/MacOS/__preview.dylib", "EXECUTABLE_DEBUG_DYLIB_INSTALL_NAME": "@rpath/learnfocus.debug.dylib", "EXECUTABLE_DEBUG_DYLIB_PATH": "learnfocus.app/Contents/MacOS/learnfocus.debug.dylib", "EXECUTABLE_FOLDER_PATH": "learnfocus.app/Contents/MacOS", "EXECUTABLE_FOLDER_PATH_SHALLOW_BUNDLE_NO": "learnfocus.app/Contents/MacOS", "EXECUTABLE_FOLDER_PATH_SHALLOW_BUNDLE_YES": "learnfocus.app/Contents", "EXECUTABLE_NAME": "learnfocus", "EXECUTABLE_PATH": "learnfocus.app/Contents/MacOS/learnfocus", "EXPANDED_CODE_SIGN_IDENTITY": "-", "EXPANDED_CODE_SIGN_IDENTITY_NAME": "Sign to Run Locally", "EXTENSIONS_FOLDER_PATH": "learnfocus.app/Contents/Extensions", "FILE_LIST": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects/LinkFileList", "FIXED_FILES_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/FixedFiles", "FLUTTER_APPLICATION_PATH": "/Users/<USER>/Documents/Work/cursor/learnfocus", "FLUTTER_BUILD_DIR": "build", "FLUTTER_BUILD_NAME": "1.0.0", "FLUTTER_BUILD_NUMBER": "1", "FLUTTER_ROOT": "/Users/<USER>/development/flutter", "FLUTTER_TARGET": "/Users/<USER>/Documents/Work/cursor/learnfocus/lib/main.dart", "FRAMEWORKS_FOLDER_PATH": "learnfocus.app/Contents/Frameworks", "FRAMEWORK_FLAG_PREFIX": "-framework", "FRAMEWORK_SEARCH_PATHS": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug ", "FRAMEWORK_VERSION": "A", "FULL_PRODUCT_NAME": "learnfocus.app", "FUSE_BUILD_PHASES": "YES", "FUSE_BUILD_SCRIPT_PHASES": "NO", "GCC3_VERSION": "3.3", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_INLINES_ARE_PRIVATE_EXTERN": "YES", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PFE_FILE_C_DIALECTS": "c objective-c c++ objective-c++", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 ", "GCC_SYMBOLS_PRIVATE_EXTERN": "NO", "GCC_TREAT_WARNINGS_AS_ERRORS": "NO", "GCC_VERSION": "com.apple.compilers.llvm.clang.1_0", "GCC_VERSION_IDENTIFIER": "com_apple_compilers_llvm_clang_1_0", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_SHADOW": "YES", "GCC_WARN_STRICT_SELECTOR_MATCH": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "GENERATED_MODULEMAP_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/GeneratedModuleMaps", "GENERATE_INFOPLIST_FILE": "NO", "GENERATE_INTERMEDIATE_TEXT_BASED_STUBS": "YES", "GENERATE_MASTER_OBJECT_FILE": "NO", "GENERATE_PKGINFO_FILE": "YES", "GENERATE_PROFILING_CODE": "NO", "GENERATE_TEXT_BASED_STUBS": "NO", "GID": "20", "GROUP": "staff", "HEADERMAP_INCLUDES_FLAT_ENTRIES_FOR_TARGET_BEING_BUILT": "YES", "HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_ALL_PRODUCT_TYPES": "YES", "HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_TARGETS_NOT_BEING_BUILT": "YES", "HEADERMAP_INCLUDES_NONPUBLIC_NONPRIVATE_HEADERS": "YES", "HEADERMAP_INCLUDES_PROJECT_HEADERS": "YES", "HEADERMAP_USES_FRAMEWORK_PREFIX_ENTRIES": "YES", "HEADERMAP_USES_VFS": "NO", "HEADER_SEARCH_PATHS": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/include ", "HIDE_BITCODE_SYMBOLS": "YES", "HOME": "/Users/<USER>", "HOST_ARCH": "x86_64", "HOST_PLATFORM": "macosx", "ICONV": "/usr/bin/iconv", "IMPLICIT_DEPENDENCY_DOMAIN": "default", "INFOPLIST_ENABLE_CFBUNDLEICONS_MERGE": "YES", "INFOPLIST_EXPAND_BUILD_SETTINGS": "YES", "INFOPLIST_FILE": "Runner/Info.plist", "INFOPLIST_OUTPUT_FORMAT": "same-as-input", "INFOPLIST_PATH": "learnfocus.app/Contents/Info.plist", "INFOPLIST_PREPROCESS": "NO", "INFOSTRINGS_PATH": "learnfocus.app/Contents/Resources/en.lproj/InfoPlist.strings", "INLINE_PRIVATE_FRAMEWORKS": "NO", "INSTALLAPI_IGNORE_SKIP_INSTALL": "YES", "INSTALLHDRS_COPY_PHASE": "NO", "INSTALLHDRS_SCRIPT_PHASE": "NO", "INSTALL_DIR": "/tmp/Runner.dst/Applications", "INSTALL_GROUP": "staff", "INSTALL_MODE_FLAG": "u+w,go-w,a+rX", "INSTALL_OWNER": "govo", "INSTALL_PATH": "/Applications", "INSTALL_ROOT": "/tmp/Runner.dst", "IOS_UNZIPPERED_TWIN_PREFIX_PATH": "/System/iOSSupport", "IS_MACCATALYST": "NO", "IS_UNOPTIMIZED_BUILD": "YES", "JAVAC_DEFAULT_FLAGS": "-J-Xms64m -J-XX:NewSize=4M -J-Dfile.encoding=UTF8", "JAVA_APP_STUB": "/System/Library/Frameworks/JavaVM.framework/Resources/MacOS/JavaApplicationStub", "JAVA_ARCHIVE_CLASSES": "YES", "JAVA_ARCHIVE_TYPE": "JAR", "JAVA_COMPILER": "/usr/bin/javac", "JAVA_FOLDER_PATH": "learnfocus.app/Contents/Resources/Java", "JAVA_FRAMEWORK_RESOURCES_DIRS": "Resources", "JAVA_JAR_FLAGS": "cv", "JAVA_SOURCE_SUBDIR": ".", "JAVA_USE_DEPENDENCIES": "YES", "JAVA_ZIP_FLAGS": "-urg", "JIKES_DEFAULT_FLAGS": "+E +OLDCSO", "KASAN_CFLAGS_CLASSIC": "-DKASAN=1 -DKASAN_CLASSIC=1 -fsanitize=address -mllvm -asan-globals-live-support -mllvm -asan-force-dynamic-shadow", "KASAN_CFLAGS_TBI": "-DKASAN=1 -DKASAN_TBI=1 -fsanitize=kernel-hwaddress -mllvm -hwasan-recover=0 -mllvm -hwasan-instrument-atomics=0 -mllvm -hwasan-instrument-stack=1 -mllvm -hwasan-generate-tags-with-calls=1 -mllvm -hwasan-instrument-with-calls=1 -mllvm -hwasan-use-short-granules=0 -mllvm -hwasan-memory-access-callback-prefix=__asan_", "KASAN_DEFAULT_CFLAGS": "-DKASAN=1 -DKASAN_CLASSIC=1 -fsanitize=address -mllvm -asan-globals-live-support -mllvm -asan-force-dynamic-shadow", "KEEP_PRIVATE_EXTERNS": "NO", "LD_DEPENDENCY_INFO_FILE": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/undefined_arch/learnfocus_dependency_info.dat", "LD_EXPORT_SYMBOLS": "YES", "LD_GENERATE_MAP_FILE": "NO", "LD_MAP_FILE_PATH": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-LinkMap-normal-undefined_arch.txt", "LD_NO_PIE": "NO", "LD_QUOTE_LINKER_ARGUMENTS_FOR_COMPILER_DRIVER": "YES", "LD_RUNPATH_SEARCH_PATHS": " @executable_path/../Frameworks", "LD_RUNPATH_SEARCH_PATHS_YES": "@loader_path/../Frameworks", "LD_SHARED_CACHE_ELIGIBLE": "Automatic", "LD_WARN_DUPLICATE_LIBRARIES": "NO", "LD_WARN_UNUSED_DYLIBS": "NO", "LEGACY_DEVELOPER_DIR": "/Applications/Xcode.app/Contents/PlugIns/Xcode3Core.ideplugin/Contents/SharedSupport/Developer", "LEX": "lex", "LIBRARY_DEXT_INSTALL_PATH": "/Library/DriverExtensions", "LIBRARY_FLAG_NOSPACE": "YES", "LIBRARY_FLAG_PREFIX": "-l", "LIBRARY_KEXT_INSTALL_PATH": "/Library/Extensions", "LIBRARY_SEARCH_PATHS": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug ", "LINKER_DISPLAYS_MANGLED_NAMES": "NO", "LINK_FILE_LIST_normal_x86_64": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.LinkFileList", "LINK_OBJC_RUNTIME": "YES", "LINK_WITH_STANDARD_LIBRARIES": "YES", "LLVM_TARGET_TRIPLE_OS_VERSION": "macos10.14", "LLVM_TARGET_TRIPLE_OS_VERSION_NO": "macos10.14", "LLVM_TARGET_TRIPLE_OS_VERSION_YES": "macos15.4", "LLVM_TARGET_TRIPLE_VENDOR": "apple", "LM_AUX_CONST_METADATA_LIST_PATH_normal_x86_64": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.SwiftConstValuesFileList", "LOCALIZATION_EXPORT_SUPPORTED": "YES", "LOCALIZATION_PREFERS_STRING_CATALOGS": "NO", "LOCALIZED_RESOURCES_FOLDER_PATH": "learnfocus.app/Contents/Resources/en.lproj", "LOCALIZED_STRING_MACRO_NAMES": "NSLocalizedString CFCopyLocalizedString", "LOCALIZED_STRING_SWIFTUI_SUPPORT": "YES", "LOCAL_ADMIN_APPS_DIR": "/Applications/Utilities", "LOCAL_APPS_DIR": "/Applications", "LOCAL_DEVELOPER_DIR": "/Library/Developer", "LOCAL_LIBRARY_DIR": "/Library", "LOCROOT": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos", "LOCSYMROOT": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos", "MACH_O_TYPE": "mh_execute", "MACOSX_DEPLOYMENT_TARGET": "10.14", "MAC_OS_X_PRODUCT_BUILD_VERSION": "24F74", "MAC_OS_X_VERSION_ACTUAL": "150500", "MAC_OS_X_VERSION_MAJOR": "150000", "MAC_OS_X_VERSION_MINOR": "150500", "MAKE_MERGEABLE": "NO", "MERGEABLE_LIBRARY": "NO", "MERGED_BINARY_TYPE": "none", "MERGE_LINKED_LIBRARIES": "NO", "METAL_LIBRARY_FILE_BASE": "default", "METAL_LIBRARY_OUTPUT_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/learnfocus.app/Contents/Resources", "MODULES_FOLDER_PATH": "learnfocus.app/Contents/Modules", "MODULE_CACHE_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/ModuleCache.noindex", "MTL_ENABLE_DEBUG_INFO": "YES", "NATIVE_ARCH": "x86_64", "NATIVE_ARCH_32_BIT": "i386", "NATIVE_ARCH_64_BIT": "x86_64", "NATIVE_ARCH_ACTUAL": "x86_64", "NO_COMMON": "YES", "OBJECT_FILE_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects", "OBJECT_FILE_DIR_normal": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal", "OBJROOT": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex", "ONLY_ACTIVE_ARCH": "YES", "OS": "MACOS", "OSAC": "/usr/bin/osacompile", "PACKAGE_CONFIG": "/Users/<USER>/Documents/Work/cursor/learnfocus/.dart_tool/package_config.json", "PACKAGE_TYPE": "com.apple.package-type.wrapper.application", "PASCAL_STRINGS": "YES", "PATH": "/Applications/Xcode.app/Contents/SharedFrameworks/SwiftBuild.framework/Versions/A/PlugIns/SWBBuildService.bundle/Contents/PlugIns/SWBUniversalPlatformPlugin.bundle/Contents/Frameworks/SWBUniversalPlatform.framework/Resources:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/Users/<USER>/.trae/sdks/workspaces/0241d5d4/versions/node/current/bin:/Users/<USER>/.trae/sdks/versions/node/current/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/.pyenv/shims:/Users/<USER>/.wasmtime/bin:/Users/<USER>/Documents/Work/ffmpeg:/usr/local/opt/openssl@1.1/bin:/Users/<USER>/Library/Android/sdk/platform-tools:/Users/<USER>/.nvm/versions/node/v20.18.1/bin:/Users/<USER>/development/flutter/bin:/Library/Frameworks/Python.framework/Versions/3.9/bin:/Library/Frameworks/Python.framework/Versions/3.11/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Applications/Wireshark.app/Contents/MacOS:/Applications/VMware Fusion.app/Contents/Public:/usr/local/go/bin:/Applications/Trae.app/Contents/Resources/app/bin:/Users/<USER>/Library/pnpm:/opt/miniconda3/bin:/opt/miniconda3/condabin:/Users/<USER>/.cargo/bin:/Users/<USER>/.rvm/bin:/Users/<USER>/Library/Android/sdk/emulator:/Users/<USER>/Library/Android/sdk/tools:/Users/<USER>/Library/Android/sdk/tools/bin:/usr/local/mysql/bin:/Users/<USER>/Documents/OpenHarmony/11/toolchains", "PATH_PREFIXES_EXCLUDED_FROM_HEADER_DEPENDENCIES": "/usr/include /usr/local/include /System/Library/Frameworks /System/Library/PrivateFrameworks /Applications/Xcode.app/Contents/Developer/Headers /Applications/Xcode.app/Contents/Developer/SDKs /Applications/Xcode.app/Contents/Developer/Platforms", "PBDEVELOPMENTPLIST_PATH": "learnfocus.app/Contents/pbdevelopment.plist", "PER_ARCH_MODULE_FILE_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/undefined_arch", "PER_ARCH_OBJECT_FILE_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/undefined_arch", "PER_VARIANT_OBJECT_FILE_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal", "PKGINFO_FILE_PATH": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/PkgInfo", "PKGINFO_PATH": "learnfocus.app/Contents/PkgInfo", "PLATFORM_DEVELOPER_APPLICATIONS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications", "PLATFORM_DEVELOPER_BIN_DIR": "/Applications/Xcode.app/Contents/Developer/usr/bin", "PLATFORM_DEVELOPER_LIBRARY_DIR": "/Applications/Xcode.app/Contents/Developer/Library", "PLATFORM_DEVELOPER_SDK_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs", "PLATFORM_DEVELOPER_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Tools", "PLATFORM_DEVELOPER_USR_DIR": "/Applications/Xcode.app/Contents/Developer/usr", "PLATFORM_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform", "PLATFORM_DISPLAY_NAME": "macOS", "PLATFORM_FAMILY_NAME": "macOS", "PLATFORM_NAME": "macosx", "PLATFORM_PREFERRED_ARCH": "x86_64", "PLATFORM_PRODUCT_BUILD_VERSION": "24E241", "PLATFORM_REQUIRES_SWIFT_AUTOLINK_EXTRACT": "NO", "PLATFORM_REQUIRES_SWIFT_MODULEWRAP": "NO", "PLIST_FILE_OUTPUT_FORMAT": "same-as-input", "PLUGINS_FOLDER_PATH": "learnfocus.app/Contents/PlugIns", "PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR": "YES", "PRECOMP_DESTINATION_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/PrefixHeaders", "PRIVATE_HEADERS_FOLDER_PATH": "learnfocus.app/Contents/PrivateHeaders", "PROCESSED_INFOPLIST_PATH": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/undefined_arch/Processed-Info.plist", "PRODUCT_BUNDLE_IDENTIFIER": "com.example.learnfocus", "PRODUCT_BUNDLE_PACKAGE_TYPE": "APPL", "PRODUCT_COPYRIGHT": "Copyright © 2025 com.example. All rights reserved.", "PRODUCT_MODULE_NAME": "learnfocus", "PRODUCT_NAME": "learnfocus", "PRODUCT_SETTINGS_PATH": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/Info.plist", "PRODUCT_TYPE": "com.apple.product-type.application", "PROFILING_CODE": "NO", "PROJECT": "Runner", "PROJECT_DERIVED_FILE_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/DerivedSources", "PROJECT_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos", "PROJECT_FILE_PATH": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner.xcodeproj", "PROJECT_GUID": "18c1723432283e0cc55f10a6dcfd9e02", "PROJECT_NAME": "Runner", "PROJECT_TEMP_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build", "PROJECT_TEMP_ROOT": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex", "PROVISIONING_PROFILE_REQUIRED": "NO", "PROVISIONING_PROFILE_REQUIRED_YES_YES": "YES", "PROVISIONING_PROFILE_SUPPORTED": "YES", "PUBLIC_HEADERS_FOLDER_PATH": "learnfocus.app/Contents/Headers", "RECOMMENDED_MACOSX_DEPLOYMENT_TARGET": "11.0", "RECURSIVE_SEARCH_PATHS_FOLLOW_SYMLINKS": "YES", "REMOVE_CVS_FROM_RESOURCES": "YES", "REMOVE_GIT_FROM_RESOURCES": "YES", "REMOVE_HEADERS_FROM_EMBEDDED_BUNDLES": "YES", "REMOVE_HG_FROM_RESOURCES": "YES", "REMOVE_STATIC_EXECUTABLES_FROM_EMBEDDED_BUNDLES": "YES", "REMOVE_SVN_FROM_RESOURCES": "YES", "RESCHEDULE_INDEPENDENT_HEADERS_PHASES": "YES", "REZ_COLLECTOR_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/ResourceManagerResources", "REZ_OBJECTS_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/ResourceManagerResources/Objects", "REZ_SEARCH_PATHS": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug ", "SCAN_ALL_SOURCE_FILES_FOR_INCLUDES": "NO", "SCRIPTS_FOLDER_PATH": "learnfocus.app/Contents/Resources/Scripts", "SCRIPT_INPUT_FILE_COUNT": "0", "SCRIPT_INPUT_FILE_LIST_COUNT": "0", "SCRIPT_OUTPUT_FILE_COUNT": "0", "SCRIPT_OUTPUT_FILE_LIST_COUNT": "0", "SDKROOT": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "SDK_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "SDK_DIR_macosx": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "SDK_DIR_macosx15_4": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "SDK_NAME": "macosx15.4", "SDK_NAMES": "macosx15.4", "SDK_PRODUCT_BUILD_VERSION": "24E241", "SDK_STAT_CACHE_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos", "SDK_STAT_CACHE_ENABLE": "YES", "SDK_STAT_CACHE_PATH": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/SDKStatCaches.noindex/macosx15.4-24E241-88b860576fb364319593bd8fb30666b0.sdkstatcache", "SDK_VERSION": "15.4", "SDK_VERSION_ACTUAL": "150400", "SDK_VERSION_MAJOR": "150000", "SDK_VERSION_MINOR": "150400", "SED": "/usr/bin/sed", "SEPARATE_STRIP": "NO", "SEPARATE_SYMBOL_EDIT": "NO", "SET_DIR_MODE_OWNER_GROUP": "YES", "SET_FILE_MODE_OWNER_GROUP": "NO", "SHALLOW_BUNDLE": "NO", "SHALLOW_BUNDLE_PLATFORM": "NO", "SHALLOW_BUNDLE_TRIPLE": "macos", "SHALLOW_BUNDLE_ios_macabi": "NO", "SHALLOW_BUNDLE_macos": "NO", "SHARED_DERIVED_FILE_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/DerivedSources", "SHARED_FRAMEWORKS_FOLDER_PATH": "learnfocus.app/Contents/SharedFrameworks", "SHARED_PRECOMPS_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/PrecompiledHeaders", "SHARED_SUPPORT_FOLDER_PATH": "learnfocus.app/Contents/SharedSupport", "SKIP_INSTALL": "NO", "SOURCE_ROOT": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos", "SRCROOT": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos", "STRINGSDATA_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/undefined_arch", "STRINGSDATA_ROOT": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build", "STRINGS_FILE_INFOPLIST_RENAME": "YES", "STRINGS_FILE_OUTPUT_ENCODING": "UTF-16", "STRIP_BITCODE_FROM_COPIED_FILES": "NO", "STRIP_INSTALLED_PRODUCT": "NO", "STRIP_PNG_TEXT": "NO", "STRIP_STYLE": "all", "STRIP_SWIFT_SYMBOLS": "YES", "SUPPORTED_PLATFORMS": "macosx", "SUPPORTS_MACCATALYST": "NO", "SUPPORTS_ON_DEMAND_RESOURCES": "NO", "SUPPORTS_TEXT_BASED_API": "NO", "SUPPRESS_WARNINGS": "NO", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_EMIT_LOC_STRINGS": "NO", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_PLATFORM_TARGET_PREFIX": "macos", "SWIFT_RESPONSE_FILE_PATH_normal_x86_64": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.SwiftFileList", "SWIFT_VERSION": "5.0", "SYMROOT": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products", "SYSTEM_ADMIN_APPS_DIR": "/Applications/Utilities", "SYSTEM_APPS_DIR": "/Applications", "SYSTEM_CORE_SERVICES_DIR": "/System/Library/CoreServices", "SYSTEM_DEMOS_DIR": "/Applications/Extras", "SYSTEM_DEVELOPER_APPS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications", "SYSTEM_DEVELOPER_BIN_DIR": "/Applications/Xcode.app/Contents/Developer/usr/bin", "SYSTEM_DEVELOPER_DEMOS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Utilities/Built Examples", "SYSTEM_DEVELOPER_DIR": "/Applications/Xcode.app/Contents/Developer", "SYSTEM_DEVELOPER_DOC_DIR": "/Applications/Xcode.app/Contents/Developer/ADC Reference Library", "SYSTEM_DEVELOPER_GRAPHICS_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Graphics Tools", "SYSTEM_DEVELOPER_JAVA_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Java Tools", "SYSTEM_DEVELOPER_PERFORMANCE_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Performance Tools", "SYSTEM_DEVELOPER_RELEASENOTES_DIR": "/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes", "SYSTEM_DEVELOPER_TOOLS": "/Applications/Xcode.app/Contents/Developer/Tools", "SYSTEM_DEVELOPER_TOOLS_DOC_DIR": "/Applications/Xcode.app/Contents/Developer/ADC Reference Library/documentation/DeveloperTools", "SYSTEM_DEVELOPER_TOOLS_RELEASENOTES_DIR": "/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes/DeveloperTools", "SYSTEM_DEVELOPER_USR_DIR": "/Applications/Xcode.app/Contents/Developer/usr", "SYSTEM_DEVELOPER_UTILITIES_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Utilities", "SYSTEM_DEXT_INSTALL_PATH": "/System/Library/DriverExtensions", "SYSTEM_DOCUMENTATION_DIR": "/Library/Documentation", "SYSTEM_EXTENSIONS_FOLDER_PATH": "learnfocus.app/Contents/Library/SystemExtensions", "SYSTEM_EXTENSIONS_FOLDER_PATH_SHALLOW_BUNDLE_NO": "learnfocus.app/Contents/Library/SystemExtensions", "SYSTEM_EXTENSIONS_FOLDER_PATH_SHALLOW_BUNDLE_YES": "learnfocus.app/Contents/SystemExtensions", "SYSTEM_KEXT_INSTALL_PATH": "/System/Library/Extensions", "SYSTEM_LIBRARY_DIR": "/System/Library", "TAPI_DEMANGLE": "YES", "TAPI_ENABLE_PROJECT_HEADERS": "NO", "TAPI_LANGUAGE": "objective-c", "TAPI_LANGUAGE_STANDARD": "compiler-default", "TAPI_USE_SRCROOT": "YES", "TAPI_VERIFY_MODE": "Pedantic", "TARGETNAME": "Runner", "TARGET_BUILD_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug", "TARGET_NAME": "Runner", "TARGET_TEMP_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build", "TEMP_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build", "TEMP_FILES_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build", "TEMP_FILE_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build", "TEMP_ROOT": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex", "TEMP_SANDBOX_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/TemporaryTaskSandboxes", "TEST_FRAMEWORK_SEARCH_PATHS": " /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "TEST_LIBRARY_SEARCH_PATHS": " /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "TOOLCHAINS": "com.apple.dt.toolchain.XcodeDefault", "TOOLCHAIN_DIR": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "TRACK_WIDGET_CREATION": "true", "TREAT_MISSING_BASELINES_AS_TEST_FAILURES": "NO", "TREAT_MISSING_SCRIPT_PHASE_OUTPUTS_AS_ERRORS": "NO", "TREE_SHAKE_ICONS": "false", "UID": "501", "UNINSTALLED_PRODUCTS_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/UninstalledProducts", "UNLOCALIZED_RESOURCES_FOLDER_PATH": "learnfocus.app/Contents/Resources", "UNLOCALIZED_RESOURCES_FOLDER_PATH_SHALLOW_BUNDLE_NO": "learnfocus.app/Contents/Resources", "UNLOCALIZED_RESOURCES_FOLDER_PATH_SHALLOW_BUNDLE_YES": "learnfocus.app/Contents", "UNSTRIPPED_PRODUCT": "NO", "USER": "govo", "USER_APPS_DIR": "/Users/<USER>/Applications", "USER_LIBRARY_DIR": "/Users/<USER>/Library", "USE_DYNAMIC_NO_PIC": "YES", "USE_HEADERMAP": "YES", "USE_HEADER_SYMLINKS": "NO", "VALIDATE_DEVELOPMENT_ASSET_PATHS": "YES_ERROR", "VALIDATE_PRODUCT": "NO", "VALID_ARCHS": "arm64 arm64e i386 x86_64", "VERBOSE_PBXCP": "NO", "VERSIONPLIST_PATH": "learnfocus.app/Contents/version.plist", "VERSION_INFO_BUILDER": "govo", "VERSION_INFO_FILE": "learnfocus_vers.c", "VERSION_INFO_STRING": "\"@(#)PROGRAM:learnfocus  PROJECT:Runner-\"", "WARNING_CFLAGS": "-Wall -Wconditional-uninitialized -Wnullable-to-nonnull-conversion -Wmissing-method-return-type -Woverlength-strings", "WORKSPACE_DIR": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos", "WRAPPER_EXTENSION": "app", "WRAPPER_NAME": "learnfocus.app", "WRAPPER_SUFFIX": ".app", "WRAP_ASSET_PACKS_IN_SEPARATE_DIRECTORIES": "NO", "XCODE_APP_SUPPORT_DIR": "/Applications/Xcode.app/Contents/Developer/Library/Xcode", "XCODE_PRODUCT_BUILD_VERSION": "16E140", "XCODE_VERSION_ACTUAL": "1630", "XCODE_VERSION_MAJOR": "1600", "XCODE_VERSION_MINOR": "1630", "XPCSERVICES_FOLDER_PATH": "learnfocus.app/Contents/XPCServices", "YACC": "yacc", "_BOOL_": "NO", "_BOOL_NO": "NO", "_BOOL_YES": "YES", "_DEVELOPMENT_TEAM_IS_EMPTY": "YES", "_DISCOVER_COMMAND_LINE_LINKER_INPUTS": "YES", "_DISCOVER_COMMAND_LINE_LINKER_INPUTS_INCLUDE_WL": "YES", "_IS_EMPTY_": "YES", "_MACOSX_DEPLOYMENT_TARGET_IS_EMPTY": "NO", "_WRAPPER_CONTENTS_DIR": "/Contents", "_WRAPPER_CONTENTS_DIR_SHALLOW_BUNDLE_NO": "/Contents", "_WRAPPER_PARENT_PATH": "/..", "_WRAPPER_PARENT_PATH_SHALLOW_BUNDLE_NO": "/..", "_WRAPPER_RESOURCES_DIR": "/Resources", "_WRAPPER_RESOURCES_DIR_SHALLOW_BUNDLE_NO": "/Resources", "__DIAGNOSE_DEPRECATED_ARCHS": "YES", "__IS_NOT_MACOS": "NO", "__IS_NOT_MACOS_macosx": "NO", "__IS_NOT_SIMULATOR": "YES", "__IS_NOT_SIMULATOR_simulator": "NO", "arch": "undefined_arch", "variant": "normal"}, "allow-missing-inputs": true, "always-out-of-date": true, "working-directory": "/Users/<USER>/Documents/Work/cursor/learnfocus/macos", "control-enabled": false, "repair-via-ownership-analysis": true, "signature": "0d570bbfffbf7167e4b8e549883826fa"}, "P2:target-Runner-****************************************************************-:Debug:SwiftDriver Compilation Requirements Runner normal x86_64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements Runner normal x86_64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/MainFlutterWindow.swift", "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Runner/AppDelegate.swift", "/Users/<USER>/Documents/Work/cursor/learnfocus/macos/Flutter/GeneratedPluginRegistrant.swift", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.SwiftFileList", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner_const_extract_protocols.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-generated-files.hmap", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-own-target-headers.hmap", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-all-target-headers.hmap", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-project-headers.hmap", "<ClangStatCache /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/SDKStatCaches.noindex/macosx15.4-24E241-88b860576fb364319593bd8fb30666b0.sdkstatcache>", "<target-Runner-****************************************************************--copy-headers-completion>", "<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner Swift Compilation Requirements Finished", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftmodule", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftsourceinfo", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.abi.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus-Swift.h", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.swiftdoc"]}, "P2:target-Runner-****************************************************************-:Debug:SwiftMergeGeneratedHeaders /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/learnfocus-Swift.h /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/learnfocus-Swift.h /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus-Swift.h", "inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus-Swift.h", "<target-Runner-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/learnfocus-Swift.h"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/Entitlements.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/Entitlements.plist", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/Entitlements.plist"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner_const_extract_protocols.json", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/Runner_const_extract_protocols.json"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.LinkFileList", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.LinkFileList"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.SwiftConstValuesFileList", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.SwiftConstValuesFileList"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.SwiftFileList", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/x86_64/learnfocus.SwiftFileList"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Script-3399D490228B24CF009A79C7.sh": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Script-3399D490228B24CF009A79C7.sh", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Script-3399D490228B24CF009A79C7.sh"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-DebugDylibInstallName-normal-x86_64.txt": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-DebugDylibInstallName-normal-x86_64.txt", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-DebugDylibInstallName-normal-x86_64.txt"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-DebugDylibPath-normal-x86_64.txt": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-DebugDylibPath-normal-x86_64.txt", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-DebugDylibPath-normal-x86_64.txt"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-all-non-framework-target-headers.hmap", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-all-non-framework-target-headers.hmap"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-all-target-headers.hmap", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-all-target-headers.hmap"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-generated-files.hmap", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-generated-files.hmap"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-own-target-headers.hmap", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-own-target-headers.hmap"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-project-headers.hmap", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus-project-headers.hmap"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.DependencyMetadataFileList", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.DependencyMetadataFileList"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.DependencyStaticMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.DependencyStaticMetadataFileList", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.DependencyStaticMetadataFileList"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.hmap", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/learnfocus.hmap"]}}}