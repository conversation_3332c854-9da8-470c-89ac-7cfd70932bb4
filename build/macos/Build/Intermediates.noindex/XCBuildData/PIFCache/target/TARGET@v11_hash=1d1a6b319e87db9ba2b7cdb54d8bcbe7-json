{"buildConfigurations": [{"baseConfigurationFileReference": "18c1723432283e0cc55f10a6dcfd9e02c4902511cd7de506ddcb4d4fe24c1302", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "CLANG_ENABLE_MODULES": "YES", "CODE_SIGN_ENTITLEMENTS": "Runner/DebugProfile.entitlements", "CODE_SIGN_STYLE": "Automatic", "COMBINE_HIDPI_IMAGES": "YES", "INFOPLIST_FILE": "Runner/Info.plist", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/../Frameworks", "PROVISIONING_PROFILE_SPECIFIER": "", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0"}, "guid": "18c1723432283e0cc55f10a6dcfd9e02414fc89210b939a670a56353deb389f9", "name": "Debug"}, {"baseConfigurationFileReference": "18c1723432283e0cc55f10a6dcfd9e02c4902511cd7de506ddcb4d4fe24c1302", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "CLANG_ENABLE_MODULES": "YES", "CODE_SIGN_ENTITLEMENTS": "Runner/Release.entitlements", "CODE_SIGN_STYLE": "Automatic", "COMBINE_HIDPI_IMAGES": "YES", "INFOPLIST_FILE": "Runner/Info.plist", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/../Frameworks", "PROVISIONING_PROFILE_SPECIFIER": "", "SWIFT_VERSION": "5.0"}, "guid": "18c1723432283e0cc55f10a6dcfd9e02591de84c5dabac79be81ba6cbbf9c7d5", "name": "Release"}, {"baseConfigurationFileReference": "18c1723432283e0cc55f10a6dcfd9e02c4902511cd7de506ddcb4d4fe24c1302", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "CLANG_ENABLE_MODULES": "YES", "CODE_SIGN_ENTITLEMENTS": "Runner/DebugProfile.entitlements", "CODE_SIGN_STYLE": "Automatic", "COMBINE_HIDPI_IMAGES": "YES", "INFOPLIST_FILE": "Runner/Info.plist", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/../Frameworks", "PROVISIONING_PROFILE_SPECIFIER": "", "SWIFT_VERSION": "5.0"}, "guid": "18c1723432283e0cc55f10a6dcfd9e02efdb1d0e782e0ff253b8b1542e0f27ef", "name": "Profile"}], "buildPhases": [{"buildFiles": [{"fileReference": "18c1723432283e0cc55f10a6dcfd9e0224b190b0ed104f7b1fa5480db83abd20", "guid": "18c1723432283e0cc55f10a6dcfd9e0282a68372325fbeb344a644fdc22a4f7e"}, {"fileReference": "18c1723432283e0cc55f10a6dcfd9e0290d3d1ed97e73aa53c07fd80de982e98", "guid": "18c1723432283e0cc55f10a6dcfd9e02a096ce12b9139e481d6bd81017d9c3bb"}, {"fileReference": "18c1723432283e0cc55f10a6dcfd9e02763b891d23f1b765a6d89a076d8c809a", "guid": "18c1723432283e0cc55f10a6dcfd9e020762c8823fdaf22393225d08db9f7bd7"}], "guid": "18c1723432283e0cc55f10a6dcfd9e0245468e5587d9dfa6699ece6600b0f31d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "18c1723432283e0cc55f10a6dcfd9e02dfc51d21b116b68082271de2adc934b6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "18c1723432283e0cc55f10a6dcfd9e02896112a93f2a09f72db0bec985ef4c64", "guid": "18c1723432283e0cc55f10a6dcfd9e02bd1f27e44757c5ac88a22e01a15f15d9"}, {"fileReference": "18c1723432283e0cc55f10a6dcfd9e028c91953baeab2ee7d542aceae751d8da", "guid": "18c1723432283e0cc55f10a6dcfd9e02c3e2c81dad01081f94f0811e7a2c7643"}], "guid": "18c1723432283e0cc55f10a6dcfd9e023cf19e18f74724910e507f60975bc54e", "type": "com.apple.buildphase.resources"}, {"buildFiles": [], "destinationSubfolder": "$(FRAMEWORKS_FOLDER_PATH)", "destinationSubpath": "", "guid": "18c1723432283e0cc55f10a6dcfd9e020d4f58b498d2216a5c31cecb5ed70906", "type": "com.apple.buildphase.copy-files"}, {"alwaysOutOfDate": "true", "alwaysRunForInstallHdrs": "false", "buildFiles": [], "emitEnvironment": "true", "guid": "18c1723432283e0cc55f10a6dcfd9e0280fce6e78b27fa69321adec3779466d5", "inputFileListPaths": [], "inputFilePaths": [], "name": "<PERSON>", "originalObjectID": "3399D490228B24CF009A79C7", "outputFileListPaths": [], "outputFilePaths": [], "sandboxingOverride": "basedOnBuildSetting", "scriptContents": "echo \"$PRODUCT_NAME.app\" > \"$PROJECT_DIR\"/Flutter/ephemeral/.app_filename && \"$FLUTTER_ROOT\"/packages/flutter_tools/bin/macos_assemble.sh embed\n", "shellPath": "/bin/sh", "type": "com.apple.buildphase.shell-script"}], "buildRules": [], "dependencies": [{"guid": "18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a", "name": "Flutter Assemble"}], "guid": "18c1723432283e0cc55f10a6dcfd9e02a26d6e580d3222b86d62fb80ec380c14", "name": "Runner", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "18c1723432283e0cc55f10a6dcfd9e0211f07a77f8580f053762d0563c8edc82", "name": "learnfocus.app", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.application", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Profile", "provisioningStyle": 0}], "type": "standard"}