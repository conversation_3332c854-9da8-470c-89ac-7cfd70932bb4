{"buildConfigurations": [{"buildSettings": {"CODE_SIGN_STYLE": "Manual", "PRODUCT_NAME": "$(TARGET_NAME)"}, "guid": "18c1723432283e0cc55f10a6dcfd9e02d5d345235681149dc98077be639d5732", "name": "Debug"}, {"buildSettings": {"CODE_SIGN_STYLE": "Automatic", "PRODUCT_NAME": "$(TARGET_NAME)"}, "guid": "18c1723432283e0cc55f10a6dcfd9e024173fbca35daa28e3bd208741257ac3f", "name": "Release"}, {"buildSettings": {"CODE_SIGN_STYLE": "Manual", "PRODUCT_NAME": "$(TARGET_NAME)"}, "guid": "18c1723432283e0cc55f10a6dcfd9e0235aef2f356a7eebaba70a2c1f0ccd906", "name": "Profile"}], "buildPhases": [{"alwaysOutOfDate": "false", "alwaysRunForInstallHdrs": "false", "buildFiles": [], "emitEnvironment": "true", "guid": "18c1723432283e0cc55f10a6dcfd9e02f4f5bcf30ee9448700f1908916ccb6e2", "inputFileListPaths": ["Flutter/ephemeral/FlutterInputs.xcfilelist"], "inputFilePaths": ["Flutter/ephemeral/tripwire"], "name": "<PERSON>", "originalObjectID": "33CC111E2044C6BF0003C045", "outputFileListPaths": ["Flutter/ephemeral/FlutterOutputs.xcfilelist"], "outputFilePaths": [], "sandboxingOverride": "basedOnBuildSetting", "scriptContents": "\"$FLUTTER_ROOT\"/packages/flutter_tools/bin/macos_assemble.sh && touch Flutter/ephemeral/tripwire", "shellPath": "/bin/sh", "type": "com.apple.buildphase.shell-script"}], "buildRules": [], "dependencies": [], "guid": "18c1723432283e0cc55f10a6dcfd9e02339362e37688461f7f8b651800569a9a", "name": "Flutter Assemble", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}], "type": "aggregate"}