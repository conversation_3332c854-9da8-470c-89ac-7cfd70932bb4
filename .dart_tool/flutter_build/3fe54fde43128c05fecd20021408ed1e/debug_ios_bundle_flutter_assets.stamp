{"inputs": ["/Users/<USER>/development/flutter/bin/internal/engine.version", "/Users/<USER>/development/flutter/bin/internal/engine.version", "/Users/<USER>/Documents/Work/cursor/learnfocus/.dart_tool/flutter_build/3fe54fde43128c05fecd20021408ed1e/app.dill", "/Users/<USER>/Documents/Work/cursor/learnfocus/.dart_tool/flutter_build/3fe54fde43128c05fecd20021408ed1e/App.framework/App", "/Users/<USER>/Documents/Work/cursor/learnfocus/pubspec.yaml", "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/targets/icon_tree_shaker.dart", "/Users/<USER>/development/flutter/bin/internal/engine.version", "/Users/<USER>/development/flutter/bin/internal/engine.version", "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/tools/shader_compiler.dart", "/Users/<USER>/development/flutter/bin/internal/engine.version", "/Users/<USER>/Documents/Work/cursor/learnfocus/pubspec.yaml", "/Users/<USER>/Documents/Work/cursor/learnfocus/ios/Runner/Info.plist", "/Users/<USER>/Documents/Work/cursor/learnfocus/ios/Flutter/AppFrameworkInfo.plist", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf", "/Users/<USER>/development/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/async-2.11.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/boolean_selector-2.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/characters-1.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/clock-1.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.18.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/cupertino_icons-1.0.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/fake_async-1.3.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_lints-4.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker-10.0.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker_flutter_testing-3.0.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker_testing-3.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/lints-4.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/matcher-0.12.16+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/material_color_utilities-0.11.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/meta-1.15.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/path-1.9.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/source_span-1.10.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/stack_trace-1.11.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/stream_channel-2.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/string_scanner-1.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/term_glyph-1.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/test_api-0.7.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/vector_math-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/vm_service-14.2.5/LICENSE", "/Users/<USER>/development/flutter/bin/cache/pkg/sky_engine/LICENSE", "/Users/<USER>/development/flutter/packages/flutter/LICENSE"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/vm_snapshot_data", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/isolate_snapshot_data", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/kernel_blob.bin", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/ios/Debug-iphonesimulator/App.framework/App", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/ios/Debug-iphonesimulator/App.framework/Info.plist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/shaders/ink_sparkle.frag", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/AssetManifest.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/AssetManifest.bin", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/FontManifest.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/NOTICES.Z"]}