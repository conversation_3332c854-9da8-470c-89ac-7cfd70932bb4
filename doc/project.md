# 间歇流（Interflow）项目技术文档

## 项目概述

间歇流（Interflow）是一个基于Flutter框架开发的跨平台专注力管理应用，采用创新的"间歇式微休息专注法"，帮助用户在90分钟的专注周期内通过随机的8-10秒微休息来维持高效专注状态。

## 技术栈

### 前端框架
- **Flutter 3.5.4+**: 跨平台移动应用开发框架
- **Dart**: 主要编程语言
- **Material Design 3**: UI设计系统

### 平台支持
- **Android**: 支持Android平台，使用Kotlin作为原生代码
- **iOS**: 支持iOS平台，使用Swift作为原生代码  
- **macOS**: 支持macOS桌面平台

### 开发工具与配置
- **分析工具**: `analysis_options.yaml` - Dart代码分析配置
- **依赖管理**: `pubspec.yaml` - Flutter项目依赖配置
- **版本控制**: Git

## 项目目录结构

```
learnfocus/
├── README.md                    # 项目说明文档
├── pubspec.yaml                 # Flutter项目配置文件
├── pubspec.lock                 # 依赖版本锁定文件
├── analysis_options.yaml        # Dart代码分析配置
├── learnfocus.iml              # IntelliJ IDEA项目配置
├── .metadata                   # Flutter项目元数据
├── .gitignore                  # Git忽略文件配置
│
├── lib/                        # 主要源代码目录
│   └── main.dart              # 应用程序入口文件
│
├── test/                       # 测试代码目录
│   └── widget_test.dart       # Widget测试文件
│
├── android/                    # Android平台特定代码
│   ├── app/src/main/kotlin/com/example/learnfocus/
│   │   └── MainActivity.kt    # Android主Activity
│   └── ...                    # 其他Android配置文件
│
├── ios/                        # iOS平台特定代码
│   ├── Runner/
│   │   ├── AppDelegate.swift  # iOS应用委托
│   │   └── Assets.xcassets/   # iOS资源文件
│   └── RunnerTests/           # iOS测试文件
│
├── macos/                      # macOS平台特定代码
│   ├── Runner/
│   │   ├── AppDelegate.swift  # macOS应用委托
│   │   └── MainFlutterWindow.swift # 主窗口
│   └── RunnerTests/           # macOS测试文件
│
├── doc/                        # 项目文档目录
│   ├── design.md              # UI设计文档
│   └── project.md             # 本技术文档
│
├── scripts/                    # 工具脚本目录
│   ├── analyze_metronome.py   # 节拍器分析工具
│   ├── analyze_metronome.md   # 分析工具文档
│   ├── app_lang_switcher.sh   # 语言切换脚本
│   ├── rebuild_android.sh     # Android重构建脚本
│   └── update_version.sh      # 版本更新脚本
│
├── build/                      # 构建输出目录
├── .dart_tool/                # Dart工具缓存
├── .idea/                     # IDE配置文件
└── .git/                      # Git版本控制
```

## 核心功能模块

### 1. 专注计时系统
- **专注周期**: 90分钟标准专注时长
- **间歇机制**: 每3-5分钟随机触发微休息
- **微休息**: 8-10秒随机时长的放松时间
- **音频提示**: 轻柔的开始/结束提示音

### 2. 用户界面模块
根据`doc/design.md`设计文档，应用包含以下主要界面：

- **主界面**: 专注会话启动和状态显示
- **专注计时界面**: 实时显示剩余时间和进度
- **小休息提示界面**: 引导用户进行微休息
- **专注完成界面**: 显示完成统计和休息建议
- **设置界面**: 时间参数和提示音配置
- **统计界面**: 数据分析和成就系统

### 3. 数据分析系统
- **时间统计**: 日/周/月专注时长记录
- **质量分析**: 完成率、平均间隔等指标
- **可视化图表**: 专注趋势和进度展示
- **成就系统**: 激励机制和进度追踪

### 4. 配置管理
- **时间参数自定义**: 专注时长、休息频率、休息时长
- **提示音设置**: 多种音效主题和自定义配置
- **个性化选项**: 适配不同用户偏好

## 技术实现特点

### 1. 跨平台架构
- 使用Flutter框架实现一套代码多平台运行
- 原生平台代码最小化，主要用于平台特定功能
- Material Design 3提供现代化UI体验

### 2. 随机算法
- 实现3-5分钟随机间隔的专注提醒
- 8-10秒随机时长的微休息控制
- 确保专注节奏的自然性和不可预测性

### 3. 音频系统
- 集成音频播放功能用于提示音
- 支持多种音效主题切换
- 自定义音频配置导入功能

### 4. 数据持久化
- 本地数据存储用于统计信息
- 用户设置和偏好保存
- 成就进度和积分系统数据管理

## 开发工具与脚本

### 分析工具
- **analyze_metronome.py**: Python脚本，用于分析节拍器录音的稳定性
  - 支持BPM分析和节拍检测
  - 生成可视化分析图表
  - 用于验证应用计时精度

### 构建脚本
- **rebuild_android.sh**: Android平台重构建脚本
- **update_version.sh**: 版本号更新自动化脚本
- **app_lang_switcher.sh**: 应用语言切换工具

## 依赖管理

### 主要依赖
```yaml
dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
```

### 环境要求
- **Dart SDK**: ^3.5.4
- **Flutter**: 最新稳定版本
- **Android**: API Level 21+
- **iOS**: iOS 12.0+
- **macOS**: macOS 10.14+

## 项目特色

### 1. 科学依据
- 基于人类注意力自然波动节律（Ultraadian Rhythm）
- 符合认知科学研究的专注力维持机制
- 温和的微恢复方式避免专注感中断

### 2. 用户体验
- 高度可定制的参数配置
- 直观简洁的界面设计
- 渐进式的成就激励系统

### 3. 技术创新
- 创新的间歇式专注算法
- 精确的随机时间控制
- 跨平台一致性体验

## 应用场景

- **深度工作**: 需要长时间专注的工作任务
- **学习备考**: 学习和考试准备的专注管理
- **创意活动**: 音乐练习、写作等需要持续心流的活动
- **防疲劳训练**: 结合呼吸技巧的专注力训练

## 未来扩展方向

1. **智能算法优化**: 基于用户行为数据优化间歇频率
2. **社交功能**: 专注小组和挑战机制
3. **健康集成**: 与健康应用的数据同步
4. **AI个性化**: 基于机器学习的个性化推荐
5. **企业版本**: 团队协作和管理功能

## 开发状态

当前项目处于初始开发阶段，主要完成了：
- ✅ 项目架构搭建
- ✅ 跨平台环境配置
- ✅ UI设计规范制定
- ✅ 技术文档编写
- 🔄 核心功能开发中
- ⏳ 测试和优化待进行

---

*本文档将随着项目开发进度持续更新*
